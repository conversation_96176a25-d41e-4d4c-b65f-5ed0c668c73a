# DataFlow Pipeline - 灵活的数据处理框架

基于 [OpenDCAI/DataFlow](https://github.com/OpenDCAI/DataFlow) 构建的灵活数据处理框架，支持预训练数据和SFT数据的全流程处理。

## 🚀 特性

- **配置化流水线**: 通过YAML/JSON配置文件定义数据处理流程
- **模块化操作符**: 支持文本清洗、分块、QA生成等多种操作符
- **智能并行处理**: 自动切分大型JSONL文件并并行处理，支持多进程和多文件并行
- **NPU设备管理**: 智能分配NPU设备给并行任务，支持PDF处理加速
- **监控和日志**: 详细的执行监控、进度跟踪和错误处理
- **命令行接口**: 友好的CLI工具，支持批量处理和参数覆盖
- **可扩展性**: 易于添加自定义操作符和处理逻辑

## 📦 安装

### 前置要求

```bash
# 安装 DataFlow 框架
pip install open-dataflow

# 或者如果需要本地GPU推理
pip install open-dataflow[vllm]
```

### 安装本框架

```bash
# 克隆项目
git clone <repository-url>
cd uc-dataflow-scripts

# 安装依赖
pip install -r requirements.txt
```

## 🛠️ 快速开始

### 1. 创建配置文件

```bash
# 创建基础配置模板
python -m dataflow_pipeline create-config --output config.yaml --template basic

# 创建文本清洗配置
python -m dataflow_pipeline create-config --output text_cleaning.yaml --template text_cleaning

# 创建QA生成配置
python -m dataflow_pipeline create-config --output qa_generation.yaml --template qa_generation
```

### 2. 运行流水线

```bash
# 运行基础流水线
python -m dataflow_pipeline run --config config.yaml

# 运行时覆盖参数
python -m dataflow_pipeline run --config config.yaml \
    --input-path ./data/new_input.jsonl \
    --parallel-workers 4 \
    --log-level DEBUG

# 批量处理多个文件
python -m dataflow_pipeline batch --config config.yaml \
    --input-dir ./data/batch_input \
    --pattern "*.jsonl" \
    --parallel 2
```

### 3. 监控和验证

```bash
# 验证配置文件
python -m dataflow_pipeline validate --config config.yaml

# 查看流水线状态
python -m dataflow_pipeline status --config config.yaml

# 列出可用操作符
python -m dataflow_pipeline list-operators --detailed
```

## 📋 配置文件格式

### 基础配置结构

```yaml
name: "my_pipeline"
description: "数据处理流水线描述"

# 存储配置
storage:
  input_path: "./data/input.jsonl"
  output_path: "./data/output.jsonl"
  cache_path: "./cache"
  file_name_prefix: "pipeline_step"
  cache_type: "jsonl"

# LLM服务配置（可选）
llm:
  api_url: "http://localhost:8008/v1/chat/completions"
  model_name: "DeepSeek-V3-0324"
  max_workers: 2
  temperature: 0.7

# 执行配置
batch_size: 100
parallel_workers: 4              # 并行工作进程数
retry_attempts: 3
log_level: "INFO"

# 并行处理配置
chunk_processing: true           # 启用大文件自动切分
max_chunk_size: 10000           # 每个块的最大行数

# NPU设备配置（可选）
npu:
  enabled: true                  # 启用NPU加速
  max_devices: 8                 # 最大NPU设备数
  device_allocation: "auto"      # 设备分配策略

# 操作符配置
operators:
  - name: "text_cleaner"
    type: "text_cleaning"
    enabled: true
    parameters:
      min_words: 5
      max_words: 10000
      dedup_threshold: 0.9
```

## 🚀 NPU设备管理

框架支持智能的NPU设备分配，特别针对PDF处理等计算密集型任务进行了优化：

### NPU设备分配策略

1. **自动分配 (auto)**: 根据worker数量和设备数量智能分配
2. **轮询分配 (round_robin)**: 循环分配设备给不同worker
3. **独占分配 (exclusive)**: 每个worker独占一个设备

### NPU配置示例

```yaml
# NPU设备配置
npu:
  enabled: true                  # 启用NPU加速
  max_devices: 8                 # 最大可用NPU设备数
  device_allocation: "auto"      # 分配策略
  # visible_devices: "0,1,2,3"  # 手动指定设备（可选）

# 并行处理配置
parallel_workers: 4              # 4个并行worker
```

### 环境变量自动设置

框架会为每个worker自动设置NPU环境变量：

- `ASCEND_RT_VISIBLE_DEVICES`: 可见的NPU设备ID
- `ASCEND_DEVICE_ID`: 当前使用的设备ID
- `RANK_ID`: 进程排名ID

### NPU加速的PDF处理

```yaml
operators:
  - name: "npu_pdf_processor"
    type: "npu_pdf"              # NPU加速的PDF处理
    parameters:
      npu_batch_size: 16         # NPU批处理大小
      npu_precision: "fp16"      # NPU精度模式
      npu_memory_fraction: 0.8   # NPU内存使用比例
```

### 使用示例

```bash
# 启用NPU处理
python -m dataflow_pipeline run \
    --config configs/npu_pdf_processing.yaml \
    --parallel-workers 4 \
    --npu-devices 8

# 指定NPU分配策略
python -m dataflow_pipeline run \
    --config config.yaml \
    --enable-npu \
    --npu-allocation exclusive

# 运行NPU演示
python demo_npu_allocation.py
```

## ⚡ 并行处理优化

框架支持两种并行处理模式，可以大幅提升大规模数据处理的效率：

### 1. 大文件自动切分并行处理

对于单个大型JSONL文件，框架会自动将其切分成多个块，并使用多进程并行处理：

```yaml
# 配置并行处理
parallel_workers: 8              # 使用8个并行进程
chunk_processing: true           # 启用自动切分
max_chunk_size: 10000           # 每块最多10000行
```

**处理流程**：
1. 自动检测大型JSONL文件
2. 按`max_chunk_size`切分成多个临时文件
3. 使用`parallel_workers`个进程并行处理各个块
4. 自动合并所有块的处理结果
5. 清理临时文件

### 2. 多文件并行处理

对于多个输入文件，框架支持文件级别的并行处理：

```bash
# 批量处理多个文件
python -m dataflow_pipeline batch \
    --config config.yaml \
    --input-dir ./data \
    --pattern "*.jsonl" \
    --parallel 4
```

### 3. 性能优化建议

- **CPU密集型任务**：设置`parallel_workers`为CPU核心数
- **I/O密集型任务**：可以设置更高的并行数
- **内存限制**：减小`max_chunk_size`以降低内存使用
- **LLM处理**：建议较小的`max_chunk_size`（如5000）避免API限制

### 4. 并行处理示例

```bash
# 处理大型文件（自动切分）
python -m dataflow_pipeline run \
    --config configs/parallel_processing_demo.yaml \
    --parallel-workers 8 \
    --max-chunk-size 5000

# 禁用切分处理（适用于小文件）
python -m dataflow_pipeline run \
    --config config.yaml \
    --no-chunk-processing
```

## 🔧 可用操作符

### 文本清洗操作符 (`text_cleaning`)

综合的文本清洗操作符，包含多种过滤和精炼功能：

```yaml
- name: "comprehensive_cleaning"
  type: "text_cleaning"
  parameters:
    # 词数过滤
    min_words: 5
    max_words: 100000
    
    # 句子数过滤
    min_sentences: 3
    max_sentences: 7500
    
    # 去重设置
    dedup_threshold: 0.9
    dedup_num_perm: 128
    dedup_ngram: 5
    
    # 内容质量过滤
    ellipsis_threshold: 0.3
    symbol_ratio_threshold: 0.4
    
    # 水印检测
    watermarks:
      - "Copyright"
      - "版权所有"
      - "保留所有权利"
```

### 文本分块操作符 (`chunking`)

将长文本分割成适当大小的块：

```yaml
- name: "text_chunker"
  type: "chunking"
  parameters:
    split_method: "sentence"  # 或 "paragraph", "token"
    chunk_size: 2048
    tokenizer_name: "/path/to/tokenizer"
    overlap_size: 200
    min_chunk_size: 100
```

### QA生成操作符 (`qa_generation`)

使用LLM生成问答对：

```yaml
- name: "qa_generator"
  type: "qa_generation"
  parameters:
    custom_prompt: |
      你是一个专业的数据生成助手...
      请根据以下文档生成问答对...
```

### PDF转Markdown操作符 (`pdf_to_markdown`)

将PDF文件转换为Markdown格式：

```yaml
- name: "pdf_converter"
  type: "pdf_to_markdown"
  parameters:
    intermediate_dir: "./temp/pdf_to_md"
    lang: "zh"
    mineru_backend: "pipeline"
```

### NPU加速PDF操作符 (`npu_pdf`)

使用NPU加速的PDF处理：

```yaml
- name: "npu_pdf_processor"
  type: "npu_pdf"
  parameters:
    # NPU配置
    npu_batch_size: 16
    npu_precision: "fp16"
    npu_memory_fraction: 0.8
    npu_optimization: true

    # PDF处理配置
    intermediate_dir: "./temp/npu_pdf"
    lang: "zh"
```

## 📊 使用示例

### 示例1: 文本清洗流水线

```bash
# 使用预定义的文本清洗配置
python -m dataflow_pipeline run --config configs/text_cleaning_pipeline.yaml
```

### 示例2: QA生成流水线

```bash
# 生成SFT训练数据
python -m dataflow_pipeline run --config configs/qa_generation_pipeline.yaml
```

### 示例3: 完整处理流水线

```bash
# 运行完整的数据处理流程：清洗 -> 分块 -> QA生成
python -m dataflow_pipeline run --config configs/full_pipeline.yaml
```

### 示例4: 批量处理

```bash
# 批量处理目录中的所有文件
python -m dataflow_pipeline batch \
    --config configs/text_cleaning_pipeline.yaml \
    --input-dir /home/<USER>/datasets/domain_行业数据/CSG行业数据/ \
    --pattern "part_*.jsonl" \
    --parallel 4
```

## 🔍 监控和日志

框架提供详细的监控和日志功能：

- **执行报告**: 自动生成JSON格式的执行报告
- **进度跟踪**: 实时显示处理进度和预计完成时间
- **错误处理**: 详细的错误日志和重试机制
- **统计信息**: 文件处理统计、成功率、执行时间等

查看执行报告：

```bash
# 查看最近的执行状态
python -m dataflow_pipeline status --cache-path ./cache

# 查看详细的执行报告
cat ./cache/execution_report.json
```

## 🎯 与现有脚本的对比

### 原有脚本的问题

1. **硬编码配置**: 参数直接写在代码中，难以调整
2. **重复代码**: 每个脚本都有相似的初始化和执行逻辑
3. **缺乏监控**: 没有统一的进度跟踪和错误处理
4. **难以扩展**: 添加新功能需要修改多个文件

### 新框架的优势

1. **配置化**: 所有参数通过配置文件管理
2. **模块化**: 操作符可以灵活组合和复用
3. **标准化**: 统一的接口和执行流程
4. **可监控**: 详细的日志和进度跟踪
5. **易扩展**: 通过工厂模式轻松添加新操作符

## 🔧 自定义操作符

添加自定义操作符：

```python
from dataflow_pipeline import BaseOperator, OperatorFactory

class CustomOperator(BaseOperator):
    def run(self, storage, input_key="raw_chunk", **kwargs):
        # 自定义处理逻辑
        return True

# 注册操作符
OperatorFactory.register_operator('custom_op', CustomOperator)
```

## 📝 配置文件模板

项目提供了多个预定义的配置模板：

- `configs/text_cleaning_pipeline.yaml`: 文本清洗流水线
- `configs/qa_generation_pipeline.yaml`: QA生成流水线
- `configs/chunking_pipeline.yaml`: 文本分块流水线
- `configs/full_pipeline.yaml`: 完整处理流水线

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个框架。

## 📄 许可证

本项目采用 Apache 2.0 许可证。
