# DataFlow Pipeline NPU设备分配 - 简化指南

## 🎯 功能概述

为DataFlow Pipeline框架添加了简单而有效的NPU设备分配功能，主要解决PDF处理等任务的NPU设备冲突问题。

## 🔧 核心功能

### 1. 自动NPU设备分配

框架会根据`parallel_workers`设置，自动为每个并行任务分配不同的NPU设备：

- Worker 0 -> NPU Device 0
- Worker 1 -> NPU Device 1  
- Worker 2 -> NPU Device 2
- ...

### 2. 环境变量自动设置

每个worker进程会自动设置：
- `ASCEND_RT_VISIBLE_DEVICES=设备ID`
- `ASCEND_DEVICE_ID=设备ID`

### 3. PDF操作符自动支持

现有的`pdf_to_markdown`操作符会自动检测并使用分配的NPU设备，无需额外配置。

## 📋 配置方法

### 基础配置

```yaml
# NPU设备配置
npu:
  enabled: true                  # 启用NPU功能
  max_devices: 8                 # 最大NPU设备数
  device_allocation: "auto"      # 分配策略

# 并行处理配置  
parallel_workers: 4              # 4个并行worker，会分配到NPU 0,1,2,3

# PDF处理操作符
operators:
  - name: "pdf_processor"
    type: "pdf_to_markdown"      # 自动支持NPU
    parameters:
      intermediate_dir: "./temp/pdf"
      lang: "zh"
      mineru_backend: "pipeline"
```

## 🚀 使用方法

### 1. 命令行使用

```bash
# 启用NPU处理
python -m dataflow_pipeline run \
    --config configs/npu_pdf_processing.yaml \
    --parallel-workers 4 \
    --enable-npu

# 指定NPU设备数量
python -m dataflow_pipeline run \
    --config config.yaml \
    --parallel-workers 8 \
    --npu-devices 8
```

### 2. 配置文件使用

使用提供的配置模板：
```bash
python -m dataflow_pipeline run --config configs/npu_pdf_processing.yaml
```

### 3. 演示和测试

```bash
# 运行NPU分配演示
python demo_npu_allocation.py
```

## 📊 设备分配策略

### Auto模式（推荐）
- 如果worker数 ≤ 设备数：每个worker独占一个设备
- 如果worker数 > 设备数：多个worker共享设备（轮询分配）

### Round Robin模式
- 循环分配：Worker 0->Device 0, Worker 1->Device 1, ..., Worker N->Device (N%设备数)

### Exclusive模式  
- 独占分配：每个worker独占一个设备，超出设备数的worker等待

## 🔍 工作原理

1. **启动时**：NPU管理器检测可用设备
2. **分配时**：根据worker ID和策略分配设备
3. **运行时**：设置环境变量`ASCEND_RT_VISIBLE_DEVICES`
4. **处理时**：PDF操作符自动使用分配的设备

## ⚠️ 注意事项

1. **硬件要求**：需要支持Ascend NPU的服务器
2. **驱动要求**：安装NPU驱动和工具链
3. **环境变量**：框架会自动设置，无需手动配置
4. **设备数量**：确保`parallel_workers`不超过可用NPU设备数（除非使用共享模式）

## 🛠️ 故障排除

### 问题1：NPU设备检测失败
```bash
# 检查NPU状态
npu-smi info
```

### 问题2：设备分配冲突
```bash
# 使用独占模式
device_allocation: "exclusive"
```

### 问题3：环境变量未设置
```bash
# 检查日志中的设备分配信息
tail -f ./cache/pipeline.log
```

## 📚 相关文件

- `configs/npu_pdf_processing.yaml` - NPU PDF处理配置模板
- `demo_npu_allocation.py` - NPU分配演示脚本
- `dataflow_pipeline/npu_manager.py` - NPU设备管理核心代码
- `dataflow_pipeline/operators.py` - 支持NPU的PDF操作符

## 🎉 总结

通过简单的配置，框架就能自动管理NPU设备分配，让PDF处理等任务充分利用NPU加速，无需复杂的手动配置。
