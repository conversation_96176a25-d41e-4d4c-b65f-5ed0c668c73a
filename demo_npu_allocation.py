#!/usr/bin/env python3
"""
NPU Device Allocation Demo

This script demonstrates the NPU device allocation functionality
for parallel PDF processing tasks.
"""

import sys
import os
import time
import json
from pathlib import Path
from typing import List

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from dataflow_pipeline.npu_manager import NPUDeviceManager, NPUProcessManager, create_npu_manager
from dataflow_pipeline.config import NPUConfig


def demo_npu_detection():
    """Demonstrate NPU device detection"""
    print("🔍 NPU Device Detection Demo")
    print("=" * 50)
    
    # Create NPU manager
    npu_manager = NPUDeviceManager(max_devices=8, allocation_strategy="auto")
    
    # Get device status
    status = npu_manager.get_status()
    
    print(f"Total NPU devices: {status['total_devices']}")
    print(f"Available devices: {status['available_devices']}")
    print(f"Allocation strategy: {status['allocation_strategy']}")
    
    print("\nDevice details:")
    for device_id, device_info in status['devices'].items():
        print(f"  Device {device_id}: Available={device_info['available']}")
    
    return npu_manager


def demo_device_allocation_strategies():
    """Demonstrate different device allocation strategies"""
    print("\n🎯 Device Allocation Strategies Demo")
    print("=" * 50)
    
    strategies = ["auto", "round_robin", "exclusive"]
    num_workers = 6
    
    for strategy in strategies:
        print(f"\n--- {strategy.upper()} Strategy ---")
        
        npu_manager = NPUDeviceManager(max_devices=4, allocation_strategy=strategy)
        allocation_map = npu_manager.get_device_allocation_map(num_workers)
        
        print(f"Workers: {num_workers}, Devices: {npu_manager.max_devices}")
        print("Allocation map:")
        for worker_id, device_id in allocation_map.items():
            print(f"  Worker {worker_id} -> NPU Device {device_id}")


def demo_environment_setup():
    """Demonstrate NPU environment variable setup"""
    print("\n🌍 NPU Environment Setup Demo")
    print("=" * 50)
    
    npu_manager = NPUDeviceManager(max_devices=8)
    
    # Simulate different worker allocations
    for worker_id in range(4):
        device_id = npu_manager.allocate_device(worker_id)
        if device_id is not None:
            env = npu_manager.get_device_env(device_id)
            
            print(f"\nWorker {worker_id} -> NPU Device {device_id}")
            print("Environment variables:")
            print(f"  ASCEND_RT_VISIBLE_DEVICES={env.get('ASCEND_RT_VISIBLE_DEVICES')}")
            print(f"  ASCEND_DEVICE_ID={env.get('ASCEND_DEVICE_ID')}")
            print(f"  RANK_ID={env.get('RANK_ID')}")


def demo_process_management():
    """Demonstrate NPU process management"""
    print("\n⚙️ NPU Process Management Demo")
    print("=" * 50)
    
    npu_manager = NPUDeviceManager(max_devices=4, allocation_strategy="exclusive")
    process_manager = NPUProcessManager(npu_manager)
    
    # Simulate starting multiple processes
    processes = []
    for worker_id in range(3):
        print(f"\nStarting worker {worker_id}...")
        
        env = process_manager.start_process_with_npu(worker_id, None)
        if env:
            processes.append(worker_id)
            device_id = env.get('ASCEND_RT_VISIBLE_DEVICES')
            print(f"  Worker {worker_id} allocated NPU device {device_id}")
        else:
            print(f"  Worker {worker_id} failed to get NPU device")
    
    # Show current status
    status = npu_manager.get_status()
    print(f"\nCurrent status: {status['available_devices']}/{status['total_devices']} devices available")
    
    # Cleanup processes
    print("\nCleaning up processes...")
    for worker_id in processes:
        process_manager.cleanup_process(worker_id)
        print(f"  Released worker {worker_id}")
    
    # Final status
    status = npu_manager.get_status()
    print(f"Final status: {status['available_devices']}/{status['total_devices']} devices available")


def demo_config_integration():
    """Demonstrate NPU configuration integration"""
    print("\n⚙️ NPU Configuration Integration Demo")
    print("=" * 50)
    
    # Create different NPU configurations
    configs = [
        {
            "name": "Auto Allocation",
            "config": NPUConfig(
                enabled=True,
                max_devices=8,
                device_allocation="auto"
            )
        },
        {
            "name": "Round Robin",
            "config": NPUConfig(
                enabled=True,
                max_devices=4,
                device_allocation="round_robin"
            )
        },
        {
            "name": "Exclusive",
            "config": NPUConfig(
                enabled=True,
                max_devices=6,
                device_allocation="exclusive"
            )
        },
        {
            "name": "Manual Devices",
            "config": NPUConfig(
                enabled=True,
                max_devices=4,
                device_allocation="auto",
                visible_devices="0,2,4,6"
            )
        }
    ]
    
    for config_info in configs:
        print(f"\n--- {config_info['name']} ---")
        
        npu_manager = create_npu_manager(config_info['config'])
        if npu_manager:
            status = npu_manager.get_status()
            print(f"  Devices: {status['total_devices']}")
            print(f"  Strategy: {status['allocation_strategy']}")
            
            # Test allocation for 4 workers
            allocation_map = npu_manager.get_device_allocation_map(4)
            print("  Worker allocation:")
            for worker_id, device_id in allocation_map.items():
                print(f"    Worker {worker_id} -> Device {device_id}")
        else:
            print("  NPU manager creation failed")


def demo_parallel_pdf_simulation():
    """Simulate parallel PDF processing with NPU allocation"""
    print("\n📄 Parallel PDF Processing Simulation")
    print("=" * 50)
    
    # Configuration
    num_workers = 4
    max_devices = 8
    
    # Create NPU manager
    npu_config = NPUConfig(
        enabled=True,
        max_devices=max_devices,
        device_allocation="auto"
    )
    
    npu_manager = create_npu_manager(npu_config)
    if not npu_manager:
        print("NPU manager not available")
        return
    
    # Get device allocation
    allocation_map = npu_manager.get_device_allocation_map(num_workers)
    
    print(f"Processing PDF files with {num_workers} workers on {max_devices} NPU devices")
    print("\nDevice allocation:")
    for worker_id, device_id in allocation_map.items():
        print(f"  Worker {worker_id} -> NPU Device {device_id}")
    
    # Simulate processing
    print("\nSimulating PDF processing...")
    for worker_id, device_id in allocation_map.items():
        env = npu_manager.get_device_env(device_id)
        
        print(f"  Worker {worker_id} starting on NPU {device_id}")
        print(f"    ASCEND_RT_VISIBLE_DEVICES={env.get('ASCEND_RT_VISIBLE_DEVICES')}")
        
        # Simulate processing time
        time.sleep(0.5)
        
        print(f"  Worker {worker_id} completed")
    
    print("\nAll PDF processing tasks completed!")


def create_sample_npu_config():
    """Create a sample NPU configuration file"""
    print("\n📝 Creating Sample NPU Configuration")
    print("=" * 50)
    
    config_path = "./demo_npu_config.yaml"
    
    config_content = """# NPU Configuration Demo
name: "npu_demo_pipeline"
description: "NPU device allocation demonstration"

storage:
  input_path: "./data/sample_pdfs.jsonl"
  output_path: "./data/npu_output.jsonl"
  cache_path: "./cache/npu_demo"

# NPU configuration
npu:
  enabled: true
  max_devices: 8
  device_allocation: "auto"
  # visible_devices: "0,1,2,3"

# Parallel processing
parallel_workers: 4
chunk_processing: true
max_chunk_size: 50

operators:
  - name: "npu_pdf_processor"
    type: "npu_pdf"
    parameters:
      npu_batch_size: 16
      npu_precision: "fp16"
      npu_memory_fraction: 0.8
      intermediate_dir: "./temp/npu_demo"
"""
    
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    print(f"Sample configuration created: {config_path}")
    print("\nTo use this configuration:")
    print(f"  python -m dataflow_pipeline run --config {config_path}")


def main():
    """Main demo function"""
    print("🚀 NPU Device Allocation Demo")
    print("=" * 60)
    
    try:
        # Run all demos
        demo_npu_detection()
        demo_device_allocation_strategies()
        demo_environment_setup()
        demo_process_management()
        demo_config_integration()
        demo_parallel_pdf_simulation()
        create_sample_npu_config()
        
        print("\n" + "=" * 60)
        print("✅ NPU allocation demo completed successfully!")
        print("=" * 60)
        
        print("\nKey features demonstrated:")
        print("1. Automatic NPU device detection")
        print("2. Multiple allocation strategies (auto, round_robin, exclusive)")
        print("3. Environment variable setup for each worker")
        print("4. Process lifecycle management")
        print("5. Configuration integration")
        print("6. Parallel PDF processing simulation")
        
        print("\nNext steps:")
        print("- Use configs/npu_pdf_processing.yaml for real NPU PDF processing")
        print("- Adjust parallel_workers and max_devices based on your hardware")
        print("- Monitor NPU utilization during processing")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
