# DataFlow Pipeline Configuration - NPU-Accelerated PDF Processing
# This configuration demonstrates NPU device allocation for PDF processing tasks

name: "npu_pdf_processing_pipeline"
description: "NPU-accelerated PDF processing with automatic device allocation"

# Storage configuration
storage:
  input_path: "./data/pdf_files.jsonl"           # JSONL file containing PDF file paths
  output_path: "./data/npu_pdf_output.jsonl"     # Processed output
  cache_path: "./cache/npu_pdf"
  file_name_prefix: "npu_pdf_step"
  cache_type: "jsonl"

# NPU configuration
npu:
  enabled: true                    # Enable NPU acceleration
  max_devices: 8                   # Maximum number of NPU devices available
  device_allocation: "auto"        # "auto", "round_robin", "exclusive"
  # visible_devices: "0,1,2,3"    # Manual device specification (optional)

# Pipeline execution settings
parallel_workers: 4               # Number of parallel workers (will be allocated to NPU devices)
chunk_processing: true            # Enable automatic chunking for large files
max_chunk_size: 100              # Smaller chunks for PDF processing
retry_attempts: 3
log_level: "INFO"

# Operators configuration
operators:
  - name: "pdf_processor"
    type: "pdf_to_markdown"
    enabled: true
    input_key: "raw_content"
    parameters:
      # PDF processing parameters
      intermediate_dir: "./temp/npu_pdf"
      lang: "zh"                   # Language for OCR
      mineru_backend: "pipeline"
