# DataFlow Pipeline Configuration - Parallel Processing Demo
# This configuration demonstrates the parallel processing capabilities
# for large JSONL files with automatic chunking

name: "parallel_processing_demo"
description: "Demonstration of parallel processing with automatic JSONL file chunking"

# Storage configuration
storage:
  input_path: "./data/large_input.jsonl"      # Large JSONL file to be processed
  output_path: "./data/parallel_output.jsonl"  # Final merged output
  cache_path: "./cache/parallel_demo"
  file_name_prefix: "parallel_step"
  cache_type: "jsonl"

# Pipeline execution settings
batch_size: 100
parallel_workers: 8              # Use 8 parallel workers for maximum throughput
retry_attempts: 3
log_level: "INFO"

# Parallel processing settings
chunk_processing: true           # Enable automatic chunking
max_chunk_size: 5000            # Split large files into 5000-line chunks

# Operators configuration - lightweight for demo
operators:
  - name: "basic_text_cleaning"
    type: "text_cleaning"
    enabled: true
    input_key: "raw_chunk"
    parameters:
      # Basic filtering only for faster processing
      min_words: 3
      max_words: 50000
      dedup_threshold: 0.95
      
      # Minimal content filters for speed
      watermarks:
        - "Copyright"
        - "版权所有"
