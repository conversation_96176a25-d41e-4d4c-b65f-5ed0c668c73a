from dataflow.operators.filter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    LanguageFilter,
    ColonEndFilter,
    WordNumberFilter,
    BlocklistFilter,
    SentenceNumberFilter,
    LineEndWithEllipsisFilter,
    ContentNullFilter,
    Mean<PERSON>ord<PERSON>ength<PERSON>ilter,
    SymbolWordRatioFilter,
    HtmlEntityFilter,
    IDCardFilter,
    NoPuncFilter,
    SpecialCharacterFilter,
    WatermarkFilter,
    CurlyBracketFilter,
    CapitalWordsFilter,
    LoremIpsumFilter,
    UniqueWordsFilter,
    CharNumberFilter,
    LineStartWithBulletpointFilter,
    LineWithJavascriptFilter,
    PairQualFilter
)
from dataflow.operators.refine import (
    HtmlUrlRemoverRefiner,
    RemoveEmojiRefiner,
    RemoveExtraSpacesRefiner
)

from dataflow.utils.storage import FileStorage

import datetime, json


class PTTextFilter_GPUPipeline():
    def __init__(self):
        self.storage = FileStorage(
            first_entry_file_name="./cache/domain_dataflow_cache_zero.jsonl",
            cache_path="/home/<USER>/datasets/cache/",
            file_name_prefix="08031242domain_dataflow_cache_step",
            cache_type="jsonl",
        )
        self.model_cache_dir = './dataflow_cache'
        # self.language_filter = LanguageFilter(allowed_languages = '__label__eng_Latn', model_cache_dir = self.model_cache_dir)
        self.remove_extra_spaces_refiner = RemoveExtraSpacesRefiner()
        self.remove_emoji_refiner = RemoveEmojiRefiner()
        self.html_remove_refiner = HtmlUrlRemoverRefiner()
        self.minhash_deduplicator = MinHashDeduplicator(num_perm=128, threshold=0.9, use_n_gram=True, ngram=5)
        self.blocklist_filter = BlocklistFilter(language='zh')
        self.word_number_filter = WordNumberFilter(min_words=5, max_words=100000)
        self.colon_end_filter = ColonEndFilter()
        self.sentence_number_filter = SentenceNumberFilter(min_sentences=3, max_sentences=7500)
        self.line_end_with_ellipsis_filter = LineEndWithEllipsisFilter(threshold=0.3)
        self.content_null_filter = ContentNullFilter()
        self.mean_word_length_filter = MeanWordLengthFilter(min_length=3, max_length=10)
        self.symbol_word_ratio_filter = SymbolWordRatioFilter(threshold=0.4)
        self.html_entity_filter = HtmlEntityFilter()
        self.id_card_filter = IDCardFilter(threshold=3)
        self.no_punc_filter = NoPuncFilter(threshold=112)
        self.special_character_filter = SpecialCharacterFilter()
        self.watermark_filter = WatermarkFilter(watermarks=['Copyright', 'Watermark', 'Confidential', '版权所有', '保留所有权利'])
        self.curly_bracket_filter = CurlyBracketFilter(threshold=0.025)
        self.capital_words_filter = CapitalWordsFilter(threshold=0.2, use_tokenizer=False)
        self.lorem_ipsum_filter = LoremIpsumFilter(threshold=3e-8)
        self.unique_words_filter = UniqueWordsFilter(threshold=0.1)
        self.char_number_filter = CharNumberFilter(threshold=100)
        self.line_start_with_bulletpoint_filter = LineStartWithBulletpointFilter(threshold=0.9)
        self.line_with_javascript_filter = LineWithJavascriptFilter(threshold=3)
        # self.quality_filter = PairQualFilter(min_score=0, max_score=10000, lang='en')

    def forward(self):
        # Initial filters
        # self.language_filter.run(
        #    storage = self.storage.step(),
        #    input_key = "raw_chunk"
        # )
        # refiners
        try: 
            self.remove_extra_spaces_refiner.run(
                storage=self.storage.step(),
                input_key="raw_chunk"
            )
            self.remove_emoji_refiner.run(
                storage=self.storage.step(),
                input_key="raw_chunk"
            )
            self.html_remove_refiner.run(
                storage=self.storage.step(),
                input_key="raw_chunk"
            )
            self.minhash_deduplicator.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            self.blocklist_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            # self.word_number_filter.run(
            #    storage = self.storage.step(),
            #    input_key="raw_chunk",
            # )
            self.colon_end_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk"
            )
            self.sentence_number_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk"
            )
            self.line_end_with_ellipsis_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk"
            )
            self.content_null_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            # self.mean_word_length_filter.run(
            #    storage = self.storage.step(),
            #    input_key="raw_chunk",
            # )
            # self.symbol_word_ratio_filter.run(
            #    storage = self.storage.step(),
            #    input_key="raw_chunk",
            # )
            self.html_entity_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            self.id_card_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            #self.no_punc_filter.run(
            #    storage=self.storage.step(),
            #    input_key="raw_chunk",
            #)
            self.special_character_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            self.watermark_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            self.curly_bracket_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            #self.capital_words_filter.run(
            #    storage=self.storage.step(),
            #    input_key="raw_chunk",
            #)
            self.lorem_ipsum_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            self.unique_words_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            self.char_number_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            self.line_start_with_bulletpoint_filter.run(
                storage=self.storage.step(),
                input_key="raw_chunk",
            )
            self.line_with_javascript_filter.run(
                storage=self.storage.step(),
                input_key='raw_chunk',
            )
            # self.quality_filter.run(
            #    storage = self.storage.step(),
            #    input_key='text',
            # )
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    # This is the entry point for the pipeline
    model = PTTextFilter_GPUPipeline()

    # ./ cache / domain_dataflow_cache_zero.jsonl 示例：
    # 每一行代表一个文件地址
    # {"raw_content": "../example_data/KBCleaningPipeline/raw1/mineru/00003899_0__白皮书__白皮书_区块链安全白皮书——技术应用篇/auto/extract/00003899_0__白皮书__白皮书_区块链安全白皮书——技术应用篇_chunk.json"}
    # {"raw_content": "../example_data/KBCleaningPipeline/raw1/mineru/00003899_0__白皮书__白皮书_区块链安全白皮书——技术应用篇/auto/extract/00003899_0__白皮书__白皮书_区块链安全白皮书——技术应用篇_chunk.json"}

    #entry_file_name = "./cache/domain_dataflow_cache_zero.jsonl"
    #entry_file_name = "./cache/2048_sentence_domain.jsonl"
    #entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_aa_560.jsonl"
    #entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_ab_560.jsonl"
    #entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/202508041618_pdf_cleaning_step_step3.jsonl"
    #entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_ad_431_md_chunked2.jsonl"
    entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/part_ac_560_chunked.jsonl"
    


    time_now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

    # 读取文件，拿到 raw_content的值，也就是要处理的文件
    with open(entry_file_name, 'r') as f:
        lines = f.readlines()
        for line in lines:
            data = json.loads(line)
            raw_content = data['chunk_path']
            if raw_content:
              print(f"处理： {raw_content}")

              file_name = raw_content.split('/')[-1].split('.')[0]

              model.storage = FileStorage(
                  first_entry_file_name=raw_content,
                  cache_path=f"/home/<USER>/datasets/cache/general_cleaning/{time_now}",
                  file_name_prefix=f"{file_name}_step",
                  cache_type="jsonl",
              )

              model.forward()
            else:
              print(f"跳过： {data}")
