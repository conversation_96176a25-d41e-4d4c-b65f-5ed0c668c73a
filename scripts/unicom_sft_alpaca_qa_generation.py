from dataflow.operators.generate import SFTGeneratorSeed
from dataflow.operators.refine import CondorRefiner
from dataflow.utils.storage import FileStorage
from dataflow.serving import APILLMServing_request 
import datetime, json



class TextPipeline():
    def __init__(self):
        self.storage = FileStorage(
            first_entry_file_name="/home/<USER>//datasets/domain_行业数据/CSG行业数据/qa_chunked_text.json",
            cache_path="./cache_qa",
            file_name_prefix="202508071257_dataflow_qa_cache_step",
            cache_type="jsonl",
        )
        self.llm_serving = APILLMServing_request(
                api_url="http://134.81021.32.16322:8008/v1/chat/completions",
                model_name="DeepSeek-V3-0324",
                max_workers=2
        )

        custom_prompt = """你是一个专业的大模型训练数据生成助手，目标是从通信行业的标准、手册、指南等技术文档中提取 ShareGPT 格式的单轮问答数据，用于构建高质量中文对话语料。

请根据以下提供的文档片段，生成高质量的单轮对话数据。每条对话仅包含一个用户问题和一个助手回答，结构清晰、内容准确。

生成要求：
- 每条对话为一个 JSON 对象，字段为：
  - conversations：一个数组，包含两条消息
    - {"role": "user", "content": "..."} 表示用户提问
    - {"role": "assistant", "content": "..."} 表示助手回答
- 语言为中文
- 用户问题要自然真实，覆盖通信技术相关概念、配置方法、原理解释等
- 助手回答要专业、准确、通俗易懂，不添加无依据内容
- 输出为 5 条对话，格式为 5 个独立的 JSON 对象（非数组），每行为一条
- 不要添加任何解释性文字、额外标点或提示

{custom_section}

请从文档中提炼出 5 条单轮对话，使用如下格式：

{"conversations": [{"role": "user", "content": "5G 中的 gNB 是什么？"}, {"role": "assistant", "content": "gNB 是 5G 无线接入网的基站节点，负责与用户设备通信并连接核心网。"}]}
{"conversations": [{"role": "user", "content": "在 NR 中，UE 是如何完成初始接入的？"}, {"role": "assistant", "content": "UE 首先接收同步信号块（SSB），完成时间和频率同步，然后通过 RACH 发起随机接入过程。"}]}
"""

        self.model_cache_dir = './dataflow_cache'
        self.processor = SFTGeneratorSeed(llm_serving=self.llm_serving, custom_prompt=custom_prompt)
        # self.refiner = CondorRefiner(llm_serving=serving)

    def forward(self):
        self.processor.run(
            storage=self.storage.step(),
            input_key="raw_chunk"
        )
        # self.refiner.run(
        #     storage=self.storage.step(),
        #     input_instruction_key='instruction',
        #     input_output_key='output'
        # )

if __name__ == "__main__":
    # This is a test entry point for the TextPipeline
    # It will run the forward method of the TextPipeline class
    # to process the data and generate the output.
    print("Running TextPipeline...")
    model = TextPipeline()
    #entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/domain_qa_aa.jsonl"
    entry_file_name = "/home/<USER>/datasets/domain_行业数据/CSG行业数据/qa.jsonl"

    time_now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

    # 读取文件，拿到 raw_content的值，也就是要处理的文件
    with open(entry_file_name, 'r') as f:
        lines = f.readlines()
        for line in lines:
            data = json.loads(line)
            raw_content = data['text_path']
            if raw_content:
              print(f"处理： {raw_content}")

              file_name = raw_content.split('/')[-1].split('.')[0]

              model.storage = FileStorage(
                  first_entry_file_name=raw_content,
                  cache_path=f"/home/<USER>/datasets/cache/sft/qa/{time_now}",
                  file_name_prefix=f"{file_name}_qa_step",
                  cache_type="jsonl",
              )

              model.forward()
            else:
              print(f"跳过： {data}")
