#!/usr/bin/env python3
"""
DataFlow Pipeline Parallel Processing Demo

This script demonstrates the parallel processing capabilities
of the DataFlow pipeline framework, including automatic JSONL
file chunking and multi-process execution.
"""

import sys
import time
import json
import random
from pathlib import Path
from typing import List

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from dataflow_pipeline import ConfigManager, PipelineManager


def create_sample_data(output_file: str, num_lines: int = 50000):
    """Create a sample JSONL file for testing parallel processing"""
    print(f"Creating sample data file: {output_file} ({num_lines} lines)")
    
    # Sample text content for different domains
    sample_texts = [
        "5G网络是第五代移动通信技术，具有高速率、低延迟、大连接的特点。",
        "云计算是一种按需提供计算资源的服务模式，包括IaaS、PaaS、SaaS三种服务类型。",
        "人工智能技术在通信领域的应用包括网络优化、故障预测、智能运维等方面。",
        "物联网技术将各种设备连接到互联网，实现设备间的数据交换和智能控制。",
        "边缘计算将计算能力下沉到网络边缘，减少数据传输延迟，提高响应速度。",
        "区块链技术具有去中心化、不可篡改、透明可追溯的特点，在金融、供应链等领域有广泛应用。",
        "大数据分析技术能够从海量数据中提取有价值的信息，支持业务决策。",
        "网络安全是信息化建设的重要保障，包括防火墙、入侵检测、数据加密等技术。"
    ]
    
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for i in range(num_lines):
            # Create varied content
            base_text = random.choice(sample_texts)
            
            # Add some variation
            if i % 3 == 0:
                text = f"第{i+1}条记录：{base_text} 这是一个详细的技术说明，包含了相关的技术细节和应用场景。"
            elif i % 3 == 1:
                text = f"技术文档 #{i+1}：{base_text} 相关技术标准和规范请参考最新的行业标准。"
            else:
                text = f"案例分析 {i+1}：{base_text} 在实际应用中需要考虑性能、安全性和可扩展性等因素。"
            
            # Create JSONL record
            record = {
                "id": f"record_{i+1:06d}",
                "raw_chunk": text,
                "metadata": {
                    "source": "demo_data",
                    "category": random.choice(["5G", "云计算", "AI", "IoT", "边缘计算", "区块链", "大数据", "网络安全"]),
                    "length": len(text),
                    "created_at": f"2024-01-{(i % 30) + 1:02d}"
                }
            }
            
            f.write(json.dumps(record, ensure_ascii=False) + '\n')
    
    print(f"Sample data created: {num_lines} records")


def run_sequential_demo():
    """Run sequential processing demo"""
    print("\n" + "="*60)
    print("🐌 Sequential Processing Demo")
    print("="*60)
    
    # Create sample data
    input_file = "./demo_data/large_sample.jsonl"
    create_sample_data(input_file, 20000)
    
    # Create configuration for sequential processing
    config_data = {
        "name": "sequential_demo",
        "description": "Sequential processing demo",
        "storage": {
            "input_path": input_file,
            "output_path": "./demo_data/sequential_output.jsonl",
            "cache_path": "./demo_data/cache/sequential"
        },
        "parallel_workers": 1,  # Sequential processing
        "chunk_processing": False,
        "operators": [
            {
                "name": "basic_cleaning",
                "type": "text_cleaning",
                "parameters": {
                    "min_words": 3,
                    "max_words": 1000,
                    "dedup_threshold": 0.95
                }
            }
        ]
    }
    
    # Save config and run
    config_manager = ConfigManager()
    config = config_manager._parse_config(config_data)
    
    start_time = time.time()
    pipeline = PipelineManager(config)
    success = pipeline.execute_pipeline()
    end_time = time.time()
    
    print(f"Sequential processing completed in {end_time - start_time:.2f} seconds")
    print(f"Success: {success}")
    
    return end_time - start_time


def run_parallel_demo():
    """Run parallel processing demo"""
    print("\n" + "="*60)
    print("🚀 Parallel Processing Demo")
    print("="*60)
    
    # Create sample data
    input_file = "./demo_data/large_sample.jsonl"
    create_sample_data(input_file, 20000)
    
    # Create configuration for parallel processing
    config_data = {
        "name": "parallel_demo",
        "description": "Parallel processing demo with automatic chunking",
        "storage": {
            "input_path": input_file,
            "output_path": "./demo_data/parallel_output.jsonl",
            "cache_path": "./demo_data/cache/parallel"
        },
        "parallel_workers": 4,  # Parallel processing
        "chunk_processing": True,
        "max_chunk_size": 5000,
        "operators": [
            {
                "name": "basic_cleaning",
                "type": "text_cleaning",
                "parameters": {
                    "min_words": 3,
                    "max_words": 1000,
                    "dedup_threshold": 0.95
                }
            }
        ]
    }
    
    # Save config and run
    config_manager = ConfigManager()
    config = config_manager._parse_config(config_data)
    
    start_time = time.time()
    pipeline = PipelineManager(config)
    success = pipeline.execute_pipeline()
    end_time = time.time()
    
    print(f"Parallel processing completed in {end_time - start_time:.2f} seconds")
    print(f"Success: {success}")
    
    return end_time - start_time


def compare_performance():
    """Compare sequential vs parallel processing performance"""
    print("\n" + "="*60)
    print("📊 Performance Comparison")
    print("="*60)
    
    # Run sequential demo
    sequential_time = run_sequential_demo()
    
    # Run parallel demo
    parallel_time = run_parallel_demo()
    
    # Calculate speedup
    speedup = sequential_time / parallel_time if parallel_time > 0 else 0
    
    print("\n" + "="*60)
    print("📈 Performance Results")
    print("="*60)
    print(f"Sequential processing time: {sequential_time:.2f} seconds")
    print(f"Parallel processing time:   {parallel_time:.2f} seconds")
    print(f"Speedup:                   {speedup:.2f}x")
    
    if speedup > 1:
        print(f"🎉 Parallel processing is {speedup:.2f}x faster!")
    else:
        print("⚠️  Parallel processing overhead detected (normal for small datasets)")


def demonstrate_chunking():
    """Demonstrate the chunking mechanism"""
    print("\n" + "="*60)
    print("✂️  File Chunking Demonstration")
    print("="*60)
    
    # Create a medium-sized sample file
    input_file = "./demo_data/chunking_demo.jsonl"
    create_sample_data(input_file, 12000)
    
    # Create a pipeline manager to demonstrate chunking
    config_data = {
        "name": "chunking_demo",
        "description": "Chunking demonstration",
        "storage": {
            "input_path": input_file,
            "output_path": "./demo_data/chunking_output.jsonl",
            "cache_path": "./demo_data/cache/chunking"
        },
        "parallel_workers": 3,
        "chunk_processing": True,
        "max_chunk_size": 4000,
        "operators": []  # No operators for pure chunking demo
    }
    
    config_manager = ConfigManager()
    config = config_manager._parse_config(config_data)
    pipeline = PipelineManager(config)
    
    # Demonstrate chunking
    print(f"Original file: {input_file}")
    print(f"File size: {Path(input_file).stat().st_size / 1024 / 1024:.2f} MB")
    
    # Count lines
    with open(input_file, 'r') as f:
        total_lines = sum(1 for _ in f)
    print(f"Total lines: {total_lines}")
    
    # Create chunks
    chunk_files = pipeline._split_jsonl_file(input_file, config.parallel_workers)
    
    print(f"\nCreated {len(chunk_files)} chunks:")
    for i, chunk_file in enumerate(chunk_files):
        with open(chunk_file, 'r') as f:
            chunk_lines = sum(1 for _ in f)
        chunk_size = Path(chunk_file).stat().st_size / 1024
        print(f"  Chunk {i+1}: {chunk_lines} lines, {chunk_size:.1f} KB")
    
    # Clean up chunks
    pipeline._cleanup_temp_files()
    print("\nChunks cleaned up successfully")


def main():
    """Main demo function"""
    print("🚀 DataFlow Pipeline Parallel Processing Demo")
    print("=" * 60)
    
    try:
        # Demonstrate chunking mechanism
        demonstrate_chunking()
        
        # Compare performance
        compare_performance()
        
        print("\n" + "="*60)
        print("✅ Demo completed successfully!")
        print("="*60)
        print("\nKey takeaways:")
        print("1. Large JSONL files are automatically split into chunks")
        print("2. Each chunk is processed in parallel using separate processes")
        print("3. Results are automatically merged into the final output")
        print("4. Parallel processing can significantly improve performance")
        print("5. The framework handles all temporary file management automatically")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
