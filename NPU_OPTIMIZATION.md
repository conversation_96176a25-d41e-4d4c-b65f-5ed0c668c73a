# DataFlow Pipeline NPU优化功能

## 🎯 优化目标

本次优化为DataFlow Pipeline框架添加了智能的NPU设备管理功能，特别针对PDF处理等计算密集型任务进行了优化。主要解决了以下问题：

1. **NPU设备冲突**: 多个并行进程争用同一NPU设备
2. **资源利用率低**: NPU设备未充分利用
3. **环境配置复杂**: 手动设置`ASCEND_RT_VISIBLE_DEVICES`容易出错
4. **负载不均衡**: 设备分配不合理导致性能瓶颈

## 🏗️ 架构设计

### NPU设备管理器 (NPUDeviceManager)

负责NPU设备的检测、分配和监控：

```python
class NPUDeviceManager:
    - 自动检测可用NPU设备
    - 支持多种分配策略
    - 实时监控设备状态
    - 环境变量自动配置
```

### NPU进程管理器 (NPUProcessManager)

管理NPU进程的生命周期：

```python
class NPUProcessManager:
    - 进程启动时分配NPU设备
    - 进程结束时释放NPU设备
    - 设备使用情况跟踪
```

## 🔧 核心功能

### 1. 智能设备分配

#### 自动分配 (auto)
```python
# 根据worker数量和设备数量智能选择策略
if num_workers <= num_devices:
    strategy = "exclusive"  # 独占模式
else:
    strategy = "round_robin"  # 轮询模式
```

#### 轮询分配 (round_robin)
```python
# 循环分配设备
device_id = worker_id % num_devices
```

#### 独占分配 (exclusive)
```python
# 每个worker独占一个设备
device_id = allocate_exclusive_device()
```

### 2. 环境变量自动设置

为每个worker进程自动设置NPU环境变量：

```python
env = {
    'ASCEND_RT_VISIBLE_DEVICES': str(device_id),
    'ASCEND_DEVICE_ID': str(device_id),
    'RANK_ID': str(device_id)
}
```

### 3. 设备状态监控

实时监控NPU设备使用情况：

```python
# 设备利用率监控
device_stats = {
    'utilization': 85.2,    # 设备利用率
    'memory_usage': 67.8,   # 内存使用率
    'temperature': 45.0     # 设备温度
}
```

## 📋 配置说明

### 基础NPU配置

```yaml
npu:
  enabled: true                  # 启用NPU功能
  max_devices: 8                 # 最大设备数
  device_allocation: "auto"      # 分配策略
  visible_devices: "0,1,2,3"     # 手动指定设备（可选）
```

### 并行处理配置

```yaml
parallel_workers: 4              # 并行worker数量
chunk_processing: true           # 启用文件切分
max_chunk_size: 100             # 适合PDF处理的小块
```

### NPU操作符配置

```yaml
operators:
  - name: "npu_pdf_processor"
    type: "npu_pdf"
    parameters:
      # NPU性能配置
      npu_batch_size: 16         # NPU批处理大小
      npu_precision: "fp16"      # 精度模式
      npu_memory_fraction: 0.8   # 内存使用比例
      npu_optimization: true     # 启用优化
      
      # PDF处理配置
      intermediate_dir: "./temp/npu_pdf"
      lang: "zh"
```

## 🚀 使用方法

### 1. 命令行使用

```bash
# 基础NPU处理
python -m dataflow_pipeline run \
    --config configs/npu_pdf_processing.yaml \
    --parallel-workers 4

# 指定NPU设备数量
python -m dataflow_pipeline run \
    --config config.yaml \
    --enable-npu \
    --npu-devices 8 \
    --npu-allocation exclusive

# 手动指定设备
ASCEND_RT_VISIBLE_DEVICES=0,2,4,6 \
python -m dataflow_pipeline run --config config.yaml
```

### 2. 编程接口使用

```python
from dataflow_pipeline import ConfigManager, PipelineManager
from dataflow_pipeline.config import NPUConfig

# 创建NPU配置
npu_config = NPUConfig(
    enabled=True,
    max_devices=8,
    device_allocation="auto"
)

# 加载配置并设置NPU
config_manager = ConfigManager()
config = config_manager.load_config('config.yaml')
config.npu = npu_config

# 运行流水线
pipeline = PipelineManager(config)
success = pipeline.execute_pipeline()
```

### 3. 设备分配演示

```bash
# 运行NPU分配演示
python demo_npu_allocation.py
```

## 📊 性能优化

### 设备分配策略选择

| 场景 | 推荐策略 | 说明 |
|------|----------|------|
| Worker数 ≤ 设备数 | exclusive | 每个worker独占设备，性能最佳 |
| Worker数 > 设备数 | round_robin | 设备共享，负载均衡 |
| 混合任务 | auto | 自动选择最优策略 |

### NPU参数调优

```yaml
# 高性能配置
npu_batch_size: 32              # 大批处理提高吞吐量
npu_precision: "fp16"           # 半精度提高速度
npu_memory_fraction: 0.9        # 充分利用显存

# 稳定性配置  
npu_batch_size: 8               # 小批处理降低内存压力
npu_precision: "fp32"           # 全精度提高精度
npu_memory_fraction: 0.6        # 保留内存余量
```

### 并行配置优化

```yaml
# PDF处理优化配置
parallel_workers: 8              # 使用所有NPU设备
max_chunk_size: 50              # 小块适合PDF处理
chunk_processing: true          # 启用文件切分

# 内存优化配置
parallel_workers: 4              # 减少并行数
max_chunk_size: 20              # 更小的块
```

## 🔍 监控和调试

### 1. 设备状态查看

```python
# 获取NPU设备状态
status = npu_manager.get_status()
print(f"可用设备: {status['available_devices']}/{status['total_devices']}")
```

### 2. 分配情况查看

```python
# 查看设备分配
allocation_map = npu_manager.get_device_allocation_map(num_workers)
for worker_id, device_id in allocation_map.items():
    print(f"Worker {worker_id} -> NPU Device {device_id}")
```

### 3. 性能监控

```bash
# 监控NPU使用情况
npu-smi info -t usages

# 查看进程NPU使用
ps aux | grep python
```

## 🚨 注意事项

### 1. 硬件要求

- 支持Ascend NPU的服务器
- 安装了NPU驱动和工具链
- 足够的NPU设备数量

### 2. 软件依赖

```bash
# 安装NPU相关依赖
pip install torch-npu
pip install ascend-toolkit
```

### 3. 环境配置

```bash
# 设置NPU环境变量
export ASCEND_HOME=/usr/local/Ascend
export PATH=$ASCEND_HOME/bin:$PATH
export LD_LIBRARY_PATH=$ASCEND_HOME/lib64:$LD_LIBRARY_PATH
```

### 4. 常见问题

**问题1**: NPU设备检测失败
```bash
# 解决方案：检查NPU驱动
npu-smi info
```

**问题2**: 设备分配冲突
```bash
# 解决方案：使用独占模式
device_allocation: "exclusive"
```

**问题3**: 内存不足
```bash
# 解决方案：减少批处理大小
npu_batch_size: 8
npu_memory_fraction: 0.6
```

## 🔮 未来扩展

1. **动态负载均衡**: 根据设备负载动态调整分配
2. **多机分布式**: 跨机器的NPU设备管理
3. **智能调度**: 基于任务类型的智能设备选择
4. **性能分析**: 详细的NPU性能分析工具
5. **故障恢复**: NPU设备故障时的自动恢复机制

## 📚 相关文档

- [并行处理优化文档](PARALLEL_PROCESSING.md)
- [配置文件说明](README.md#配置文件格式)
- [操作符文档](README.md#可用操作符)
- [NPU配置示例](configs/npu_pdf_processing.yaml)
