"""
DataFlow Pipeline Command Line Interface

This module provides a comprehensive CLI for managing and executing
data processing pipelines with various configuration options.
"""

import argparse
import sys
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
import json
import yaml

from .config import Config<PERSON><PERSON><PERSON>, PipelineConfig, create_default_config
from .pipeline_manager import PipelineManager
from .operators import OperatorFactory

logger = logging.getLogger(__name__)


class DataFlowCLI:
    """Command line interface for DataFlow pipelines"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.pipeline_manager: Optional[PipelineManager] = None
    
    def create_parser(self) -> argparse.ArgumentParser:
        """Create argument parser"""
        parser = argparse.ArgumentParser(
            description="DataFlow Pipeline - Flexible data processing framework",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Run pipeline with config file
  python -m dataflow_pipeline run --config config.yaml
  
  # Run with parameter overrides
  python -m dataflow_pipeline run --config config.yaml --input-path ./data/new_input.jsonl
  
  # Create default config
  python -m dataflow_pipeline create-config --output config_template.yaml
  
  # List available operators
  python -m dataflow_pipeline list-operators
  
  # Validate configuration
  python -m dataflow_pipeline validate --config config.yaml
            """
        )
        
        subparsers = parser.add_subparsers(dest='command', help='Available commands')
        
        # Run command
        run_parser = subparsers.add_parser('run', help='Run a data processing pipeline')
        run_parser.add_argument('--config', '-c', required=True, help='Configuration file path')
        run_parser.add_argument('--input-path', help='Override input path')
        run_parser.add_argument('--output-path', help='Override output path')
        run_parser.add_argument('--cache-path', help='Override cache path')
        run_parser.add_argument('--parallel-workers', type=int, help='Number of parallel workers')
        run_parser.add_argument('--batch-size', type=int, help='Batch size for processing')
        run_parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='Log level')
        run_parser.add_argument('--chunk-processing', action='store_true', help='Enable chunk processing for large files')
        run_parser.add_argument('--no-chunk-processing', action='store_true', help='Disable chunk processing')
        run_parser.add_argument('--max-chunk-size', type=int, help='Maximum lines per chunk')
        run_parser.add_argument('--dry-run', action='store_true', help='Validate configuration without running')
        run_parser.add_argument('--resume', action='store_true', help='Resume from last checkpoint')
        
        # Create config command
        config_parser = subparsers.add_parser('create-config', help='Create a default configuration file')
        config_parser.add_argument('--output', '-o', required=True, help='Output configuration file path')
        config_parser.add_argument('--template', choices=['basic', 'text_cleaning', 'qa_generation', 'pdf_processing'], 
                                 default='basic', help='Configuration template type')
        
        # List operators command
        list_parser = subparsers.add_parser('list-operators', help='List available operators')
        list_parser.add_argument('--detailed', action='store_true', help='Show detailed operator information')
        
        # Validate command
        validate_parser = subparsers.add_parser('validate', help='Validate configuration file')
        validate_parser.add_argument('--config', '-c', required=True, help='Configuration file path')
        
        # Batch command
        batch_parser = subparsers.add_parser('batch', help='Run batch processing on multiple files')
        batch_parser.add_argument('--config', '-c', required=True, help='Configuration file path')
        batch_parser.add_argument('--input-dir', required=True, help='Input directory containing files')
        batch_parser.add_argument('--pattern', default='*.jsonl', help='File pattern to match')
        batch_parser.add_argument('--parallel', type=int, default=1, help='Number of parallel processes')
        
        return parser
    
    def run_pipeline(self, args) -> bool:
        """Run the data processing pipeline"""
        try:
            # Load configuration
            config = self.config_manager.load_config(args.config)
            
            # Apply command line overrides
            self._apply_overrides(config, args)
            
            # Validate configuration
            if not self.config_manager.validate_config(config):
                logger.error("Configuration validation failed")
                return False
            
            if args.dry_run:
                logger.info("Dry run completed successfully - configuration is valid")
                return True
            
            # Create and run pipeline
            self.pipeline_manager = PipelineManager(config)
            
            logger.info(f"Starting pipeline: {config.name}")
            success = self.pipeline_manager.execute_pipeline()
            
            if success:
                logger.info("Pipeline completed successfully")
            else:
                logger.error("Pipeline completed with errors")
            
            return success

        # exit when file not found
        except FileNotFoundError as e:
            logger.error(f"File not found: {e}")
            exit(1)

        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            return False
    
    def _apply_overrides(self, config: PipelineConfig, args):
        """Apply command line parameter overrides"""
        if args.input_path:
            config.storage.input_path = args.input_path
        if args.output_path:
            config.storage.output_path = args.output_path
        if args.cache_path:
            config.storage.cache_path = args.cache_path
        if args.parallel_workers:
            config.parallel_workers = args.parallel_workers
        if args.batch_size:
            config.batch_size = args.batch_size
        if args.log_level:
            config.log_level = args.log_level
        if args.chunk_processing:
            config.chunk_processing = True
        if args.no_chunk_processing:
            config.chunk_processing = False
        if args.max_chunk_size:
            config.max_chunk_size = args.max_chunk_size
    
    def create_config(self, args) -> bool:
        """Create a configuration file"""
        try:
            if args.template == 'basic':
                config = create_default_config()
            elif args.template == 'text_cleaning':
                config = self._create_text_cleaning_config()
            elif args.template == 'qa_generation':
                config = self._create_qa_generation_config()
            elif args.template == 'pdf_processing':
                config = self._create_pdf_processing_config()
            else:
                config = create_default_config()
            
            self.config_manager.save_config(config, args.output)
            logger.info(f"Configuration template created: {args.output}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create configuration: {e}")
            return False
    
    def _create_text_cleaning_config(self) -> PipelineConfig:
        """Create text cleaning configuration template"""
        from .config import StorageConfig, OperatorConfig
        
        storage = StorageConfig(
            input_path="./data/input.jsonl",
            output_path="./data/cleaned_output.jsonl",
            cache_path="./cache/text_cleaning"
        )
        
        operators = [
            OperatorConfig(
                name="comprehensive_text_cleaning",
                type="text_cleaning",
                parameters={
                    "min_words": 5,
                    "max_words": 10000,
                    "dedup_threshold": 0.9,
                    "min_sentences": 3,
                    "max_sentences": 1000,
                    "watermarks": ["Copyright", "版权所有", "保留所有权利"]
                }
            )
        ]
        
        return PipelineConfig(
            name="text_cleaning_pipeline",
            description="Comprehensive text cleaning pipeline",
            storage=storage,
            operators=operators
        )
    
    def _create_qa_generation_config(self) -> PipelineConfig:
        """Create QA generation configuration template"""
        from .config import StorageConfig, LLMConfig, OperatorConfig
        
        storage = StorageConfig(
            input_path="./data/chunked_text.jsonl",
            output_path="./data/qa_output.jsonl",
            cache_path="./cache/qa_generation"
        )
        
        llm = LLMConfig(
            api_url="http://localhost:8008/v1/chat/completions",
            model_name="DeepSeek-V3-0324",
            max_workers=2
        )
        
        operators = [
            OperatorConfig(
                name="qa_generator",
                type="qa_generation",
                parameters={
                    "custom_prompt": "Generate QA pairs from the given text..."
                }
            )
        ]
        
        return PipelineConfig(
            name="qa_generation_pipeline",
            description="QA generation pipeline for SFT data",
            storage=storage,
            llm=llm,
            operators=operators
        )
    
    def _create_pdf_processing_config(self) -> PipelineConfig:
        """Create PDF processing configuration template"""
        from .config import StorageConfig, OperatorConfig
        
        storage = StorageConfig(
            input_path="./data/pdf_extracted.jsonl",
            output_path="./data/pdf_processed.jsonl",
            cache_path="./cache/pdf_processing"
        )
        
        operators = [
            OperatorConfig(
                name="pdf_cleaner",
                type="pdf_cleaning",
                parameters={
                    "min_words": 10,
                    "max_words": 50000,
                    "dedup_threshold": 0.9
                }
            ),
            OperatorConfig(
                name="text_chunker",
                type="chunking",
                parameters={
                    "split_method": "sentence",
                    "chunk_size": 2048,
                    "tokenizer_name": "/home/<USER>/yulan/YuLan-Mini"
                }
            )
        ]
        
        return PipelineConfig(
            name="pdf_processing_pipeline",
            description="PDF processing and chunking pipeline",
            storage=storage,
            operators=operators
        )
    
    def list_operators(self, args) -> bool:
        """List available operators"""
        try:
            operators = OperatorFactory.list_operators()
            
            print("Available Operators:")
            print("=" * 50)
            
            for op_type in operators:
                if args.detailed:
                    info = OperatorFactory.get_operator_info(op_type)
                    print(f"\n{op_type}:")
                    print(f"  Class: {info['class']}")
                    print(f"  Base: {info['base_class']}")
                    print(f"  Description: {info['description']}")
                else:
                    print(f"  - {op_type}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to list operators: {e}")
            return False
    
    def validate_config(self, args) -> bool:
        """Validate configuration file"""
        try:
            config = self.config_manager.load_config(args.config)
            
            if self.config_manager.validate_config(config):
                print(f"✓ Configuration is valid: {args.config}")
                return True
            else:
                print(f"✗ Configuration validation failed: {args.config}")
                return False
                
        except Exception as e:
            print(f"✗ Configuration error: {e}")
            return False
    
    def run_batch(self, args) -> bool:
        """Run batch processing"""
        try:
            # Load configuration
            config = self.config_manager.load_config(args.config)

            # Find input files
            input_dir = Path(args.input_dir)
            if not input_dir.exists():
                logger.error(f"Input directory not found: {input_dir}")
                return False
            
            input_files = list(input_dir.glob(args.pattern))
            if not input_files:
                logger.error(f"No files found matching pattern: {args.pattern}")
                return False
            
            logger.info(f"Found {len(input_files)} files to process")
            
            # Create and run pipeline
            self.pipeline_manager = PipelineManager(config)
            success = self.pipeline_manager.execute_pipeline([str(f) for f in input_files])
            
            return success
            
        except Exception as e:
            logger.error(f"Batch processing failed: {e}")
            return False
    
    def main(self, args=None):
        """Main CLI entry point"""
        parser = self.create_parser()
        parsed_args = parser.parse_args(args)
        
        if not parsed_args.command:
            parser.print_help()
            return 1
        
        # Setup basic logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Execute command
        success = False
        
        if parsed_args.command == 'run':
            success = self.run_pipeline(parsed_args)
        elif parsed_args.command == 'create-config':
            success = self.create_config(parsed_args)
        elif parsed_args.command == 'list-operators':
            success = self.list_operators(parsed_args)
        elif parsed_args.command == 'validate':
            success = self.validate_config(parsed_args)
        elif parsed_args.command == 'batch':
            success = self.run_batch(parsed_args)
        else:
            parser.print_help()
            return 1
        
        return 0 if success else 1


def main():
    """Entry point for CLI"""
    cli = DataFlowCLI()
    sys.exit(cli.main())


if __name__ == '__main__':
    main()
