"""
DataFlow Pipeline Operators Module

This module provides a unified interface for all data processing operators,
including filters, refiners, generators, and custom operators.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Type
import logging
from dataflow.utils.storage import FileStorage
from dataflow.serving import APILLMServing_request
from dataflow.webui import max_tokens

logger = logging.getLogger(__name__)


class BaseOperator(ABC):
    """Base class for all operators"""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        self.name = name
        self.parameters = parameters or {}
        self.enabled = True
        
    @abstractmethod
    def run(self, storage: FileStorage, input_key: str = "raw_chunk", **kwargs) -> bool:
        """Execute the operator"""
        pass
    
    def validate_parameters(self) -> bool:
        """Validate operator parameters"""
        return True
    
    def get_info(self) -> Dict[str, Any]:
        """Get operator information"""
        return {
            'name': self.name,
            'type': self.__class__.__name__,
            'parameters': self.parameters,
            'enabled': self.enabled
        }


class FilterOperator(BaseOperator):
    """Base class for filter operators"""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        super().__init__(name, parameters)
        self.operator_instance = None
    
    def _create_operator_instance(self):
        """Create the actual DataFlow operator instance"""
        pass
    
    def run(self, storage: FileStorage, input_key: str = "raw_chunk", **kwargs) -> bool:
        """Run filter operator"""
        try:
            if not self.operator_instance:
                self._create_operator_instance()
            
            if self.operator_instance and self.enabled:
                self.operator_instance.run(
                    storage=storage,
                    input_key=input_key
                )
                logger.info(f"Filter operator '{self.name}' completed successfully")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Filter operator '{self.name}' failed: {e}")
            return False

class RefinerOperator(BaseOperator):
    """Base class for refiner operators"""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        super().__init__(name, parameters)
        self.operator_instance = None
    
    def _create_operator_instance(self):
        """Create the actual DataFlow operator instance"""
        pass
    
    def run(self, storage: FileStorage, input_key: str = "raw_chunk", **kwargs) -> bool:
        """Run refiner operator"""
        try:
            if not self.operator_instance:
                self._create_operator_instance()
            
            if self.operator_instance and self.enabled:
                self.operator_instance.run(
                    storage=storage,
                    input_key=input_key
                )
                logger.info(f"Refiner operator '{self.name}' completed successfully")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Refiner operator '{self.name}' failed: {e}")
            return False


class GeneratorOperator(BaseOperator):
    """Base class for generator operators"""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None, llm_serving=None):
        super().__init__(name, parameters)
        self.llm_serving = llm_serving
        self.operator_instance = None
    
    def _create_operator_instance(self):
        """Create the actual DataFlow operator instance"""
        pass
    
    def run(self, storage: FileStorage, input_key: str = "raw_chunk", **kwargs) -> bool:
        """Run generator operator"""
        try:
            if not self.operator_instance:
                self._create_operator_instance()
            
            if self.operator_instance and self.enabled:
                self.operator_instance.run(
                    storage=storage,
                    input_key=input_key
                )
                logger.info(f"Generator operator '{self.name}' completed successfully")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Generator operator '{self.name}' failed: {e}")
            return False


# Specific operator implementations
class TextCleaningOperator(FilterOperator):
    """Text cleaning filter operator"""

    def _create_operator_instance(self):
        from dataflow.operators.refine import (
            RemoveExtraSpacesRefiner, RemoveEmojiRefiner, HtmlUrlRemoverRefiner,
            HtmlEntityRefiner, ReferenceRemoverRefiner, RemoveEmoticonsRefiner,
            RemoveImageRefsRefiner, RemoveRepetitionsPunctuationRefiner
        )

        # Create multiple operators based on parameters
        self.operators = []

        # Add refiners first
        self.operators.append(RemoveExtraSpacesRefiner())
        self.operators.append(RemoveEmojiRefiner())
        self.operators.append(HtmlUrlRemoverRefiner())
        self.operators.append(HtmlEntityRefiner())
        self.operators.append(ReferenceRemoverRefiner())
        self.operators.append(RemoveEmoticonsRefiner())
        self.operators.append(RemoveImageRefsRefiner())
        self.operators.append(RemoveRepetitionsPunctuationRefiner())

        # Add deduplication
        if 'dedup_threshold' in self.parameters:
            from dataflow.operators.filter import MinHashDeduplicator
            threshold = self.parameters.get('dedup_threshold', 0.9)
            num_perm = self.parameters.get('dedup_num_perm', 128)
            ngram = self.parameters.get('dedup_ngram', 5)
            self.operators.append(MinHashDeduplicator(
                num_perm=num_perm, threshold=threshold, use_n_gram=True, ngram=ngram
            ))

        # Add content filters
        if 'blocklist_filter' in self.parameters:
            from dataflow.operators.filter import BlocklistFilter
            language = self.parameters.get('blocklist_filter_language', 'zh')
            self.operators.append(BlocklistFilter(language=language))


        if 'min_words' in self.parameters or 'max_words' in self.parameters:
            from dataflow.operators.filter import WordNumberFilter
            min_words = self.parameters.get('min_words', 5)
            max_words = self.parameters.get('max_words', 100000)
            self.operators.append(WordNumberFilter(min_words=min_words, max_words=max_words))

        from dataflow.operators.filter import ColonEndFilter
        self.operators.append(ColonEndFilter())

        if 'min_sentences' in self.parameters or 'max_sentences' in self.parameters:
            from dataflow.operators.filter import SentenceNumberFilter
            min_sentences = self.parameters.get('min_sentences', 3)
            max_sentences = self.parameters.get('max_sentences', 7500)
            self.operators.append(SentenceNumberFilter(min_sentences=min_sentences, max_sentences=max_sentences))

        if 'ellipsis_threshold' in self.parameters:
            from dataflow.operators.filter import LineEndWithEllipsisFilter
            threshold = self.parameters.get('ellipsis_threshold', 0.3)
            self.operators.append(LineEndWithEllipsisFilter(threshold=threshold))

        from dataflow.operators.filter import ContentNullFilter
        self.operators.append(ContentNullFilter())

        if 'mean_word_length_min' in self.parameters or 'mean_word_length_max' in self.parameters:
            from dataflow.operators.filter import MeanWordLengthFilter
            min_length = self.parameters.get('mean_word_length_min', 3)
            max_length = self.parameters.get('mean_word_length_max', 10)
            self.operators.append(MeanWordLengthFilter(min_length=min_length, max_length=max_length))

        if 'symbol_ratio_threshold' in self.parameters:
            from dataflow.operators.filter import SymbolWordRatioFilter
            threshold = self.parameters.get('symbol_ratio_threshold', 0.4)
            self.operators.append(SymbolWordRatioFilter(threshold=threshold))

        from dataflow.operators.filter import HtmlEntityFilter
        self.operators.append(HtmlEntityFilter())

        if 'id_card_threshold' in self.parameters:
            from dataflow.operators.filter import IDCardFilter
            threshold = self.parameters.get('id_card_threshold', 3)
            self.operators.append(IDCardFilter(threshold=threshold))

        from dataflow.operators.filter import SpecialCharacterFilter
        self.operators.append(SpecialCharacterFilter())

        if 'watermarks' in self.parameters:
            from dataflow.operators.filter import WatermarkFilter
            watermarks = self.parameters.get('watermarks', ['Copyright', 'Watermark', 'Confidential', '版权所有', '保留所有权利'])
            self.operators.append(WatermarkFilter(watermarks=watermarks))

        if 'curly_bracket_threshold' in self.parameters:
            from dataflow.operators.filter import CurlyBracketFilter
            threshold = self.parameters.get('curly_bracket_threshold', 0.025)
            self.operators.append(CurlyBracketFilter(threshold=threshold))

        if 'unique_words_threshold' in self.parameters:
            from dataflow.operators.filter import UniqueWordsFilter
            threshold = self.parameters.get('unique_words_threshold', 0.1)
            self.operators.append(UniqueWordsFilter(threshold=threshold))

        if 'char_number_threshold' in self.parameters:
            from dataflow.operators.filter import CharNumberFilter
            threshold = self.parameters.get('char_number_threshold', 100)
            self.operators.append(CharNumberFilter(threshold=threshold))

        if 'bulletpoint_threshold' in self.parameters:
            from dataflow.operators.filter import LineStartWithBulletpointFilter
            threshold = self.parameters.get('bulletpoint_threshold', 0.9)
            self.operators.append(LineStartWithBulletpointFilter(threshold=threshold))

        if 'javascript_threshold' in self.parameters:
            from dataflow.operators.filter import LineWithJavascriptFilter
            threshold = self.parameters.get('javascript_threshold', 3)
            self.operators.append(LineWithJavascriptFilter(threshold=threshold))

    def run(self, storage: FileStorage, input_key: str = "raw_chunk", **kwargs) -> bool:
        """Run all cleaning operators in sequence"""
        try:
            if not hasattr(self, 'operators'):
                self._create_operator_instance()

            for operator in self.operators:
                if self.enabled:
                    operator.run(storage=storage, input_key=input_key)

            logger.info(f"Text cleaning operator '{self.name}' completed successfully")
            return True

        except Exception as e:
            logger.error(f"Text cleaning operator '{self.name}' failed: {e}")
            return False


class PDF2MDOperator(BaseOperator):
    """PDF to Markdown conversion operator"""

    def run(self, storage: FileStorage, input_key: str = "raw_content", **kwargs) -> bool:
        """Run PDF cleaning operators"""
        try:
            from dataflow.operators.generate import FileOrURLToMarkdownConverterBatch

            intermediate_dir = self.parameters.get('intermediate_dir', './temp/pdf_to_md')
            lang = self.parameters.get('lang', 'zh')
            mineru_backend = self.parameters.get('mineru_backend', 'pipeline')
            available_npu_nums = self.parameters.get('available_npu_nums', 1)

            operator_instance = FileOrURLToMarkdownConverterBatch(
                intermediate_dir=intermediate_dir,
                lang=lang,
                mineru_backend=mineru_backend
            )

            if operator_instance and self.enabled:
                operator_instance.run(storage=storage, input_key=input_key)

            logger.info(f"PDF cleaning operator '{self.name}' completed successfully")
            return True

        except Exception as e:
            logger.error(f"PDF cleaning operator '{self.name}' failed: {e}")
            return False


class ChunkingOperator(GeneratorOperator):
    """Text chunking operator"""

    def run(self, storage: FileStorage, input_key: str = "text_path", **kwargs) -> bool:
        """Run text chunking"""
        try:
            from dataflow.operators.generate import CorpusTextSplitterBatch

            split_method = self.parameters.get('split_method', 'sentence')
            chunk_size = self.parameters.get('chunk_size', 2048)
            tokenizer_name = self.parameters.get('tokenizer_name', '/home/<USER>/yulan/YuLan-Mini')

            operator_instance = CorpusTextSplitterBatch(
                split_method=split_method,
                chunk_size=chunk_size,
                tokenizer_name=tokenizer_name
            )

            if operator_instance and self.enabled:
                operator_instance.run(
                    storage=storage,
                    input_key=input_key
                )
                logger.info(f"Chunking operator '{self.name}' completed successfully")
                return True
            return False

        except Exception as e:
            logger.error(f"Chunking operator '{self.name}' failed: {e}")
            return False


class QAGenerationOperator(GeneratorOperator):
    """QA generation operator"""
    
    def _create_operator_instance(self):
        from dataflow.operators.generate import SFTGeneratorSeed
        
        custom_prompt = self.parameters.get('custom_prompt', self._get_default_prompt())
        max_tokens = self.parameters.get('max_tokens', 2048)
        
        self.operator_instance = SFTGeneratorSeed(
            llm_serving=self.llm_serving,
            custom_prompt=custom_prompt,
            max_tokens=max_tokens
        )
    
    def _get_default_prompt(self):
        """Get default QA generation prompt"""
        return """你是一个专业的大模型训练数据生成助手，目标是从通信行业的标准、手册、指南等技术文档中提取 ShareGPT 格式的单轮问答数据，用于构建高质量中文对话语料。

请根据以下提供的文档片段，生成高质量的单轮对话数据。每条对话仅包含一个用户问题和一个助手回答，结构清晰、内容准确。

生成要求：
- 每条对话为一个 JSON 对象，字段为：
  - conversations：一个数组，包含两条消息
    - {"role": "user", "content": "..."} 表示用户提问
    - {"role": "assistant", "content": "..."} 表示助手回答
- 语言为中文
- 用户问题要自然真实，覆盖通信技术相关概念、配置方法、原理解释等
- 助手回答要专业、准确、通俗易懂，不添加无依据内容
- 输出为 5 条对话，格式为 5 个独立的 JSON 对象（非数组），每行为一条
- 不要添加任何解释性文字、额外标点或提示

{custom_section}

请从文档中提炼出 5 条单轮对话，使用如下格式：

{"conversations": [{"role": "user", "content": "5G 中的 gNB 是什么？"}, {"role": "assistant", "content": "gNB 是 5G 无线接入网的基站节点，负责与用户设备通信并连接核心网。"}]}
{"conversations": [{"role": "user", "content": "在 NR 中，UE 是如何完成初始接入的？"}, {"role": "assistant", "content": "UE 首先接收同步信号块（SSB），完成时间和频率同步，然后通过 RACH 发起随机接入过程。"}]}
"""


class LLMCleaningOperator(GeneratorOperator):
    """LLM-based cleaning operator"""

    def _create_operator_instance(self):
        # This would use LLM for intelligent cleaning
        # Implementation depends on specific LLM cleaning requirements
        pass

    def run(self, storage: FileStorage, input_key: str = "raw_chunk", **kwargs) -> bool:
        """Run LLM-based cleaning"""
        try:
            # Custom LLM cleaning logic here
            from dataflow.operators.generate import KnowledgeCleanerBatch
            self.operator_instance = KnowledgeCleanerBatch(
                llm_serving=self.llm_serving,
                lang="zh"
            )
            logger.info(f"LLM cleaning operator '{self.name}' completed successfully")
            return True
        except Exception as e:
            logger.error(f"LLM cleaning operator '{self.name}' failed: {e}")
            return False


class BatchProcessingOperator(BaseOperator):
    """Batch processing operator for handling multiple files"""

    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        super().__init__(name, parameters)
        self.batch_size = parameters.get('batch_size', 100) if parameters else 100
        self.input_files = []

    def set_input_files(self, files: List[str]):
        """Set input files for batch processing"""
        self.input_files = files

    def run(self, storage: FileStorage, input_key: str = "raw_chunk", **kwargs) -> bool:
        """Run batch processing"""
        try:
            # Process files in batches
            for i in range(0, len(self.input_files), self.batch_size):
                batch = self.input_files[i:i + self.batch_size]
                logger.info(f"Processing batch {i//self.batch_size + 1}: {len(batch)} files")

                # Process each file in the batch
                for file_path in batch:
                    # Custom batch processing logic here
                    pass

            logger.info(f"Batch processing operator '{self.name}' completed successfully")
            return True

        except Exception as e:
            logger.error(f"Batch processing operator '{self.name}' failed: {e}")
            return False


class OperatorFactory:
    """Factory for creating operators"""

    _operator_registry: Dict[str, Type[BaseOperator]] = {
        'text_cleaning': TextCleaningOperator,
        'pdf_to_markdown': PDF2MDOperator,
        'chunking': ChunkingOperator,
        'qa_generation': QAGenerationOperator,
        'llm_cleaning': LLMCleaningOperator,
        'batch_processing': BatchProcessingOperator,
    }
    
    @classmethod
    def register_operator(cls, name: str, operator_class: Type[BaseOperator]):
        """Register a new operator type"""
        cls._operator_registry[name] = operator_class
        logger.info(f"Registered operator: {name}")
    
    @classmethod
    def create_operator(cls, operator_type: str, name: str, 
                       parameters: Dict[str, Any] = None, 
                       llm_serving=None) -> BaseOperator:
        """Create an operator instance"""
        if operator_type not in cls._operator_registry:
            raise ValueError(f"Unknown operator type: {operator_type}")
        
        operator_class = cls._operator_registry[operator_type]
        
        # Check if operator needs LLM serving
        if issubclass(operator_class, GeneratorOperator):
            return operator_class(name, parameters, llm_serving)
        else:
            return operator_class(name, parameters)
    
    @classmethod
    def list_operators(cls) -> List[str]:
        """List all available operator types"""
        return list(cls._operator_registry.keys())
    
    @classmethod
    def get_operator_info(cls, operator_type: str) -> Dict[str, Any]:
        """Get information about an operator type"""
        if operator_type not in cls._operator_registry:
            raise ValueError(f"Unknown operator type: {operator_type}")
        
        operator_class = cls._operator_registry[operator_type]
        return {
            'type': operator_type,
            'class': operator_class.__name__,
            'base_class': operator_class.__bases__[0].__name__,
            'description': operator_class.__doc__ or "No description available"
        }
