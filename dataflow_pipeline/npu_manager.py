"""
NPU Device Manager for DataFlow Pipeline

This module provides NPU device allocation and management for parallel processing,
specifically for PDF processing tasks that require NPU acceleration.
"""

import os
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import threading
from queue import Queue
import subprocess

logger = logging.getLogger(__name__)


@dataclass
class NPUDevice:
    """NPU device information"""
    device_id: int
    is_available: bool = True
    current_process: Optional[int] = None
    memory_usage: float = 0.0
    utilization: float = 0.0


class NPUDeviceManager:
    """Manager for NPU device allocation and monitoring"""
    
    def __init__(self, max_devices: int = 8, allocation_strategy: str = "auto"):
        self.max_devices = max_devices
        self.allocation_strategy = allocation_strategy
        self.devices: Dict[int, NPUDevice] = {}
        self.device_queue = Queue()
        self.lock = threading.Lock()
        
        # Initialize devices
        self._initialize_devices()
        
    def _initialize_devices(self):
        """Initialize NPU devices"""
        try:
            # Detect available NPU devices
            available_devices = self._detect_npu_devices()
            
            # Limit to max_devices
            device_count = min(len(available_devices), self.max_devices)
            
            for i in range(device_count):
                device = NPUDevice(device_id=i)
                self.devices[i] = device
                self.device_queue.put(i)
            
            logger.info(f"Initialized {device_count} NPU devices")
            
        except Exception as e:
            logger.warning(f"Failed to initialize NPU devices: {e}")
            # Fallback: create virtual devices
            for i in range(self.max_devices):
                device = NPUDevice(device_id=i)
                self.devices[i] = device
                self.device_queue.put(i)
    
    def _detect_npu_devices(self) -> List[int]:
        """Detect available NPU devices"""
        try:
            # Try to use npu-smi to detect devices
            result = subprocess.run(['npu-smi', 'info'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # Parse npu-smi output to get device count
                lines = result.stdout.split('\n')
                device_count = 0
                for line in lines:
                    if 'NPU' in line and 'Device' in line:
                        device_count += 1
                
                return list(range(device_count))
            else:
                logger.warning("npu-smi not available, using default device count")
                return list(range(self.max_devices))
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("NPU detection failed, using default device count")
            return list(range(self.max_devices))
    
    def allocate_device(self, worker_id: int) -> Optional[int]:
        """Allocate an NPU device for a worker"""
        with self.lock:
            if self.allocation_strategy == "round_robin":
                return self._allocate_round_robin(worker_id)
            elif self.allocation_strategy == "exclusive":
                return self._allocate_exclusive()
            else:  # auto
                return self._allocate_auto(worker_id)
    
    def _allocate_round_robin(self, worker_id: int) -> int:
        """Allocate device using round-robin strategy"""
        device_id = worker_id % len(self.devices)
        logger.debug(f"Allocated NPU device {device_id} to worker {worker_id} (round-robin)")
        return device_id
    
    def _allocate_exclusive(self) -> Optional[int]:
        """Allocate device exclusively (one worker per device)"""
        if not self.device_queue.empty():
            device_id = self.device_queue.get()
            self.devices[device_id].is_available = False
            logger.debug(f"Allocated NPU device {device_id} exclusively")
            return device_id
        else:
            logger.warning("No available NPU devices for exclusive allocation")
            return None
    
    def _allocate_auto(self, worker_id: int) -> int:
        """Allocate device automatically based on availability"""
        # If we have enough devices for exclusive allocation, use that
        if len(self.devices) >= worker_id + 1:
            return worker_id % len(self.devices)
        else:
            # Use round-robin for shared allocation
            return self._allocate_round_robin(worker_id)
    
    def release_device(self, device_id: int):
        """Release an NPU device"""
        with self.lock:
            if device_id in self.devices:
                self.devices[device_id].is_available = True
                self.devices[device_id].current_process = None
                
                if self.allocation_strategy == "exclusive":
                    self.device_queue.put(device_id)
                
                logger.debug(f"Released NPU device {device_id}")
    
    def get_device_env(self, device_id: int) -> Dict[str, str]:
        """Get environment variables for NPU device"""
        env = os.environ.copy()
        env['ASCEND_RT_VISIBLE_DEVICES'] = str(device_id)
        
        # Additional NPU environment variables
        env['ASCEND_DEVICE_ID'] = str(device_id)
        env['RANK_ID'] = str(device_id)
        
        logger.debug(f"Set ASCEND_RT_VISIBLE_DEVICES={device_id}")
        return env
    
    def get_device_allocation_map(self, num_workers: int) -> Dict[int, int]:
        """Get device allocation map for all workers"""
        allocation_map = {}
        
        for worker_id in range(num_workers):
            device_id = self.allocate_device(worker_id)
            if device_id is not None:
                allocation_map[worker_id] = device_id
        
        return allocation_map
    
    def get_status(self) -> Dict[str, any]:
        """Get NPU device status"""
        with self.lock:
            status = {
                'total_devices': len(self.devices),
                'available_devices': sum(1 for d in self.devices.values() if d.is_available),
                'allocation_strategy': self.allocation_strategy,
                'devices': {}
            }
            
            for device_id, device in self.devices.items():
                status['devices'][device_id] = {
                    'available': device.is_available,
                    'current_process': device.current_process,
                    'memory_usage': device.memory_usage,
                    'utilization': device.utilization
                }
        
        return status
    
    def monitor_devices(self) -> Dict[int, Dict[str, float]]:
        """Monitor NPU device usage"""
        device_stats = {}
        
        try:
            # Try to get device statistics using npu-smi
            result = subprocess.run(['npu-smi', 'info', '-t', 'usages'], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                # Parse npu-smi output
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'NPU' in line and '%' in line:
                        # Parse device usage information
                        parts = line.split()
                        if len(parts) >= 3:
                            try:
                                device_id = int(parts[1])
                                utilization = float(parts[2].replace('%', ''))
                                memory_usage = float(parts[3].replace('%', '')) if len(parts) > 3 else 0.0
                                
                                device_stats[device_id] = {
                                    'utilization': utilization,
                                    'memory_usage': memory_usage
                                }
                                
                                # Update device info
                                if device_id in self.devices:
                                    self.devices[device_id].utilization = utilization
                                    self.devices[device_id].memory_usage = memory_usage
                                    
                            except (ValueError, IndexError):
                                continue
            
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.debug("NPU monitoring not available")
        
        return device_stats


class NPUProcessManager:
    """Manager for NPU-enabled processes"""
    
    def __init__(self, npu_manager: NPUDeviceManager):
        self.npu_manager = npu_manager
        self.process_device_map: Dict[int, int] = {}
    
    def start_process_with_npu(self, worker_id: int, target_func, *args, **kwargs):
        """Start a process with NPU device allocation"""
        # Allocate NPU device
        device_id = self.npu_manager.allocate_device(worker_id)
        
        if device_id is None:
            logger.warning(f"No NPU device available for worker {worker_id}")
            return None
        
        # Get environment with NPU settings
        env = self.npu_manager.get_device_env(device_id)
        
        # Store mapping
        self.process_device_map[worker_id] = device_id
        
        logger.info(f"Starting worker {worker_id} with NPU device {device_id}")
        
        # Return environment for process creation
        return env
    
    def cleanup_process(self, worker_id: int):
        """Clean up process and release NPU device"""
        if worker_id in self.process_device_map:
            device_id = self.process_device_map[worker_id]
            self.npu_manager.release_device(device_id)
            del self.process_device_map[worker_id]
            logger.info(f"Released NPU device {device_id} from worker {worker_id}")


def create_npu_manager(npu_config) -> Optional[NPUDeviceManager]:
    """Create NPU manager from configuration"""
    if not npu_config or not npu_config.enabled:
        return None
    
    try:
        manager = NPUDeviceManager(
            max_devices=npu_config.max_devices,
            allocation_strategy=npu_config.device_allocation
        )
        
        # Override with manual device specification if provided
        if npu_config.visible_devices:
            os.environ['ASCEND_RT_VISIBLE_DEVICES'] = npu_config.visible_devices
            logger.info(f"Set manual NPU devices: {npu_config.visible_devices}")
        
        return manager
        
    except Exception as e:
        logger.error(f"Failed to create NPU manager: {e}")
        return None


def get_optimal_npu_allocation(num_workers: int, max_devices: int) -> str:
    """Get optimal NPU allocation strategy"""
    if num_workers <= max_devices:
        return "exclusive"  # One device per worker
    else:
        return "round_robin"  # Share devices among workers
