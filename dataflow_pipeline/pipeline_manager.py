"""
DataFlow Pipeline Manager

This module provides the core pipeline management functionality,
including pipeline execution and error handling.
"""

import json
import datetime
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from dataflow.utils.storage import FileStorage
from dataflow.serving import APILLMServing_request

from .config import PipelineConfig, ConfigManager
from .operators import OperatorFactory, BaseOperator

logger = logging.getLogger(__name__)


class PipelineManager:
    """Core pipeline manager for executing data processing workflows"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.operators: List[BaseOperator] = []
        self.llm_serving = None
        
        # Setup logging
        self._setup_logging()
        
        # Initialize LLM serving if configured
        if config.llm:
            self._setup_llm_serving()
        
        # Initialize operators
        self._initialize_operators()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f"{self.config.storage.cache_path}/pipeline.log")
            ]
        )
    
    def _setup_llm_serving(self):
        """Initialize LLM serving"""
        try:
            self.llm_serving = APILLMServing_request(
                api_url=self.config.llm.api_url,
                model_name=self.config.llm.model_name,
                max_workers=self.config.llm.max_workers
            )
            logger.info(f"LLM serving initialized: {self.config.llm.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize LLM serving: {e}")
            raise
    
    def _initialize_operators(self):
        """Initialize all operators from configuration"""
        self.operators = []
        
        for op_config in self.config.operators:
            if not op_config.enabled:
                logger.info(f"Skipping disabled operator: {op_config.name}")
                continue
            
            try:
                operator = OperatorFactory.create_operator(
                    operator_type=op_config.type,
                    name=op_config.name,
                    parameters=op_config.parameters,
                    llm_serving=self.llm_serving
                )
                
                if operator.validate_parameters():
                    self.operators.append(operator)
                    logger.info(f"Initialized operator: {op_config.name} ({op_config.type})")
                else:
                    logger.error(f"Invalid parameters for operator: {op_config.name}")
                    
            except Exception as e:
                logger.error(f"Failed to initialize operator {op_config.name}: {e}")
                if self.config.retry_attempts == 0:
                    raise
    
    def execute_pipeline(self, input_files: Optional[List[str]] = None) -> bool:
        """Execute the complete pipeline"""
        start_time = time.time()
        
        try:
            logger.info(f"Starting pipeline execution: {self.config.name}")
            
            # Determine input files
            if input_files is None:
                input_files = self._get_input_files()
            
            if not input_files:
                logger.error("No input files found")
                return False
            
            # Process files
            success_count = 0
            total_files = len(input_files)
            
            if self.config.parallel_workers > 1:
                success_count = self._execute_parallel(input_files)
            else:
                success_count = self._execute_sequential(input_files)

            # Calculate statistics
            execution_time = time.time() - start_time
            
            # Log results
            success_rate = (success_count / total_files) * 100 if total_files > 0 else 0
            logger.info(f"Pipeline completed: {success_count}/{total_files} files processed successfully ({success_rate:.1f}%)")
            logger.info(f"Total execution time: {execution_time:.2f} seconds")

            return success_count == total_files
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            return False
    
    def _get_input_files(self) -> List[str]:
        """Get list of input files to process"""
        input_path = Path(self.config.storage.input_path)
        
        if input_path.is_file():
            return [str(input_path)]
        elif input_path.is_dir():
            # Find all JSON/JSONL files in directory
            files = []
            for pattern in ['*.json', '*.jsonl']:
                files.extend(input_path.glob(pattern))
            return [str(f) for f in files]
        else:
            logger.error(f"Input path not found: {input_path}")
            return []
    
    def _execute_sequential(self, input_files: List[str]) -> int:
        """Execute pipeline sequentially"""
        success_count = 0
        
        for i, input_file in enumerate(input_files):
            logger.info(f"Processing file {i+1}/{len(input_files)}: {input_file}")
            
            if self._process_single_file(input_file):
                success_count += 1
        
        return success_count
    
    def _execute_parallel(self, input_files: List[str]) -> int:
        """Execute pipeline in parallel"""
        success_count = 0
        completed_count = 0
        
        with ThreadPoolExecutor(max_workers=self.config.parallel_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self._process_single_file, input_file): input_file
                for input_file in input_files
            }
            
            # Process completed tasks
            for future in as_completed(future_to_file):
                input_file = future_to_file[future]
                completed_count += 1
                
                try:
                    if future.result():
                        success_count += 1
                        logger.info(f"Successfully processed: {input_file}")
                    else:
                        logger.error(f"Failed to process: {input_file}")
                        
                except Exception as e:
                    logger.error(f"Error processing {input_file}: {e}")
        
        return success_count
    
    def _process_single_file(self, input_file: str) -> bool:
        """Process a single input file through the pipeline"""
        try:
            # Create unique storage for this file
            file_name = Path(input_file).stem
            time_stamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            
            storage = FileStorage(
                first_entry_file_name=input_file,
                cache_path=f"{self.config.storage.cache_path}/{time_stamp}",
                file_name_prefix=f"{file_name}_{self.config.storage.file_name_prefix}",
                cache_type=self.config.storage.cache_type
            )
            
            # Execute operators in sequence
            for operator in self.operators:
                if not operator.enabled:
                    continue
                
                retry_count = 0
                while retry_count <= self.config.retry_attempts:
                    try:
                        success = operator.run(storage=storage.step())
                        if success:
                            break
                        else:
                            retry_count += 1
                            if retry_count <= self.config.retry_attempts:
                                logger.warning(f"Retrying operator {operator.name} (attempt {retry_count})")
                            
                    except Exception as e:
                        retry_count += 1
                        logger.error(f"Operator {operator.name} failed (attempt {retry_count}): {e}")
                        
                        if retry_count > self.config.retry_attempts:
                            logger.error(f"Operator {operator.name} failed after {self.config.retry_attempts} retries")
                            return False
                        
                        time.sleep(1)  # Brief delay before retry
            
            logger.info(f"Successfully processed file: {input_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to process file {input_file}: {e}")
            return False
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """Get pipeline information"""
        return {
            'name': self.config.name,
            'description': self.config.description,
            'operators': [op.get_info() for op in self.operators],
            'storage': {
                'input_path': self.config.storage.input_path,
                'output_path': self.config.storage.output_path,
                'cache_path': self.config.storage.cache_path
            },
            'configuration': {
                'batch_size': self.config.batch_size,
                'parallel_workers': self.config.parallel_workers,
                'retry_attempts': self.config.retry_attempts,
                'log_level': self.config.log_level
            }
        }
