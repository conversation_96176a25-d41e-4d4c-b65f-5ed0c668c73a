# DataFlow Pipeline 并行处理优化

## 🚀 优化概述

本次优化为DataFlow Pipeline框架添加了强大的并行处理能力，特别针对大型JSONL文件的处理进行了优化。主要改进包括：

1. **自动文件切分**: 大型JSONL文件自动切分成多个块
2. **多进程并行**: 使用ProcessPoolExecutor实现真正的并行处理
3. **智能调度**: 根据文件大小和worker数量智能选择处理策略
4. **结果合并**: 自动合并所有块的处理结果
5. **资源管理**: 自动清理临时文件和资源

## 📊 性能提升

### 处理模式对比

| 处理模式 | 适用场景 | 性能特点 | 资源使用 |
|----------|----------|----------|----------|
| 顺序处理 | 小文件、调试 | 稳定、简单 | 低CPU、低内存 |
| 文件并行 | 多个文件 | 中等提升 | 中等CPU、低内存 |
| 块并行 | 大型JSONL | 显著提升 | 高CPU、中等内存 |

### 预期性能提升

- **4核CPU**: 2-3倍性能提升
- **8核CPU**: 4-6倍性能提升
- **16核CPU**: 8-12倍性能提升

*实际性能取决于数据复杂度和操作符类型*

## 🔧 配置说明

### 基础并行配置

```yaml
# 并行处理配置
parallel_workers: 4              # 并行工作进程数
chunk_processing: true           # 启用自动切分
max_chunk_size: 10000           # 每个块的最大行数
```

### 高级配置选项

```yaml
# 针对不同场景的优化配置

# CPU密集型任务（文本清洗、过滤）
parallel_workers: 8              # 使用所有CPU核心
max_chunk_size: 15000           # 较大的块减少开销

# I/O密集型任务（文件读写）
parallel_workers: 12             # 可以超过CPU核心数
max_chunk_size: 8000            # 中等大小的块

# LLM处理任务（QA生成）
parallel_workers: 4              # 避免API限制
max_chunk_size: 2000            # 小块避免超时
```

## 🛠️ 使用方法

### 1. 命令行使用

```bash
# 基础并行处理
python -m dataflow_pipeline run \
    --config config.yaml \
    --parallel-workers 8

# 自定义块大小
python -m dataflow_pipeline run \
    --config config.yaml \
    --parallel-workers 4 \
    --max-chunk-size 5000

# 禁用切分（小文件）
python -m dataflow_pipeline run \
    --config config.yaml \
    --no-chunk-processing
```

### 2. 配置文件使用

```yaml
name: "parallel_text_cleaning"
description: "并行文本清洗流水线"

storage:
  input_path: "./data/large_dataset.jsonl"
  output_path: "./data/cleaned_output.jsonl"
  cache_path: "./cache/parallel"

# 并行配置
parallel_workers: 8
chunk_processing: true
max_chunk_size: 10000

operators:
  - name: "text_cleaner"
    type: "text_cleaning"
    parameters:
      min_words: 5
      dedup_threshold: 0.9
```

### 3. 编程接口使用

```python
from dataflow_pipeline import ConfigManager, PipelineManager

# 加载配置
config_manager = ConfigManager()
config = config_manager.load_config('parallel_config.yaml')

# 运行时覆盖并行设置
config.parallel_workers = 8
config.max_chunk_size = 5000

# 执行流水线
pipeline = PipelineManager(config)
success = pipeline.execute_pipeline()
```

## 📈 性能调优指南

### 1. 确定最优worker数量

```bash
# 测试不同worker数量
for workers in 2 4 8 16; do
    echo "Testing with $workers workers..."
    time python -m dataflow_pipeline run \
        --config config.yaml \
        --parallel-workers $workers
done
```

### 2. 优化块大小

- **大块**: 减少进程间开销，但增加内存使用
- **小块**: 更好的负载均衡，但增加管理开销
- **推荐**: 从10000行开始，根据性能调整

### 3. 内存优化

```yaml
# 内存受限环境
parallel_workers: 4              # 减少并行数
max_chunk_size: 5000            # 减小块大小

# 内存充足环境
parallel_workers: 16             # 增加并行数
max_chunk_size: 20000           # 增大块大小
```

## 🔍 监控和调试

### 1. 进度监控

框架提供实时进度监控：

```
Processing large JSONL file with 8 parallel workers
Created 4 chunk files
Chunk 1/4 completed: large_sample_chunk_0000.jsonl
Chunk 2/4 completed: large_sample_chunk_0001.jsonl
...
Successfully merged all chunk results
```

### 2. 性能分析

```bash
# 查看执行报告
cat ./cache/execution_report.json

# 查看详细日志
tail -f ./cache/pipeline.log
```

### 3. 调试模式

```bash
# 启用详细日志
python -m dataflow_pipeline run \
    --config config.yaml \
    --log-level DEBUG \
    --parallel-workers 2
```

## 🚨 注意事项

### 1. 资源限制

- **CPU**: 不要设置超过物理核心数太多的worker
- **内存**: 监控内存使用，避免OOM
- **磁盘**: 确保有足够的临时存储空间

### 2. LLM API限制

```yaml
# LLM处理的特殊配置
llm:
  max_workers: 1                 # 每个进程只用1个LLM worker
  
parallel_workers: 4              # 但可以有多个进程
max_chunk_size: 2000            # 小块避免超时
```

### 3. 错误处理

- 单个块失败不会影响其他块
- 自动重试机制
- 详细的错误日志

## 🧪 测试和验证

### 1. 运行演示

```bash
# 运行并行处理演示
python demo_parallel_processing.py

# 运行性能对比测试
python demo_parallel_processing.py
```

### 2. 验证结果

```bash
# 比较顺序和并行处理的结果
diff sequential_output.jsonl parallel_output.jsonl

# 检查行数是否一致
wc -l sequential_output.jsonl parallel_output.jsonl
```

## 📚 最佳实践

### 1. 配置模板

针对不同场景使用不同的配置模板：

- `configs/text_cleaning_pipeline.yaml`: 文本清洗
- `configs/qa_generation_pipeline.yaml`: QA生成
- `configs/parallel_processing_demo.yaml`: 性能测试

### 2. 渐进式优化

1. 从默认配置开始
2. 逐步增加worker数量
3. 调整块大小
4. 监控性能指标
5. 找到最优配置

### 3. 生产环境部署

```yaml
# 生产环境推荐配置
parallel_workers: 8              # 根据服务器配置调整
chunk_processing: true
max_chunk_size: 10000
retry_attempts: 3
log_level: "INFO"
```

## 🔮 未来改进

1. **动态负载均衡**: 根据处理速度动态调整块大小
2. **GPU加速**: 支持GPU并行处理
3. **分布式处理**: 跨机器的分布式处理
4. **智能缓存**: 中间结果缓存和复用
5. **实时监控**: Web界面的实时监控
